#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from contextlib import asynccontextmanager
import uvicorn

from src.config.settings import settings
from src.utils.database import database
from src.utils.ad_database import ad_database
from src.routes import user, admin, health, tasks, accounts
from src.api import log_config
from src.services.task_scheduler import task_scheduler

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时初始化数据库
    await database.init_database()
    await ad_database.init_database()
    print("数据库初始化完成")

    # 启动任务调度器
    await task_scheduler.start()
    print("任务调度器启动完成")

    yield

    # 关闭时的清理工作
    await task_scheduler.shutdown()
    print("任务调度器已关闭")
    print("应用关闭")

# 创建FastAPI应用
app = FastAPI(
    title=settings.APP_NAME,
    version=settings.APP_VERSION,
    description="基于FastAPI的Vue Element Admin后端API",
    lifespan=lifespan
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

# 全局异常处理
@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    """HTTP异常处理器"""
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "code": exc.status_code * 100 if exc.status_code == 4 else exc.status_code,
            "message": exc.detail,
            "data": None
        }
    )

@app.exception_handler(Exception)
async def general_exception_handler(request, exc):
    """通用异常处理器"""
    return JSONResponse(
        status_code=500,
        content={
            "code": 500,
            "message": f"服务器内部错误: {str(exc)}",
            "data": None
        }
    )

# 注册路由
app.include_router(health.router)
app.include_router(user.router)
app.include_router(admin.router)
app.include_router(tasks.router)

app.include_router(log_config.router)
app.include_router(accounts.router)

@app.get("/", summary="根路径")
async def root():
    """根路径欢迎信息"""
    return {
        "message": "欢迎使用Vue Element Admin API",
        "version": settings.APP_VERSION,
        "docs": "/docs",
        "redoc": "/redoc"
    }

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG
    )
