#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实时输出测试脚本
专门用于测试实时日志功能
"""

import time
import sys
from datetime import datetime

def main():
    print(f"[{datetime.now().strftime('%H:%M:%S')}] 实时输出测试开始")
    sys.stdout.flush()
    
    print("=" * 50)
    sys.stdout.flush()
    
    # 测试1: 快速输出
    print("测试1: 快速连续输出")
    sys.stdout.flush()
    for i in range(5):
        print(f"  快速输出 {i+1}/5")
        sys.stdout.flush()
        time.sleep(1)
    
    print("测试1完成")
    sys.stdout.flush()
    print("=" * 50)
    sys.stdout.flush()
    
    # 测试2: 慢速输出
    print("测试2: 慢速输出（每3秒一行）")
    sys.stdout.flush()
    for i in range(5):
        print(f"  慢速输出 {i+1}/5 - 当前时间: {datetime.now().strftime('%H:%M:%S')}")
        sys.stdout.flush()
        time.sleep(3)
    
    print("测试2完成")
    sys.stdout.flush()
    print("=" * 50)
    sys.stdout.flush()
    
    # 测试3: 进度条模拟
    print("测试3: 进度条模拟")
    sys.stdout.flush()
    for i in range(10):
        progress = (i + 1) * 10
        bar = "█" * (i + 1) + "░" * (9 - i)
        print(f"  进度: [{bar}] {progress}%")
        sys.stdout.flush()
        time.sleep(2)
    
    print("测试3完成")
    sys.stdout.flush()
    print("=" * 50)
    sys.stdout.flush()
    
    # 测试4: 错误输出
    print("测试4: 错误输出测试")
    sys.stdout.flush()
    print("这是标准输出", file=sys.stdout)
    sys.stdout.flush()
    print("这是错误输出", file=sys.stderr)
    sys.stderr.flush()
    
    print("=" * 50)
    sys.stdout.flush()
    
    print(f"[{datetime.now().strftime('%H:%M:%S')}] 实时输出测试完成")
    sys.stdout.flush()
    
    print("总结:")
    print("- 快速输出: 5行")
    print("- 慢速输出: 5行")
    print("- 进度条: 10行")
    print("- 错误输出: 1行")
    print("- 总计约60秒执行时间")
    sys.stdout.flush()

if __name__ == "__main__":
    main()
