#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
长时间运行的测试脚本
用于测试实时日志功能
"""

import time
import sys
from datetime import datetime

def print_flush(msg):
    """打印并立即刷新输出缓冲区"""
    print(msg)
    sys.stdout.flush()

def main():
    print_flush(f"[{datetime.now()}] 长时间运行测试脚本启动")
    print_flush("=" * 50)
    
    # 模拟初始化过程
    print_flush("正在初始化...")
    for i in range(5):
        print_flush(f"初始化步骤 {i+1}/5")
        time.sleep(2)

    print_flush("初始化完成！")
    print_flush("=" * 50)

    # 模拟数据处理过程
    print_flush("开始数据处理...")
    for i in range(10):
        print_flush(f"处理数据批次 {i+1}/10")
        print_flush(f"  - 读取数据: {i*100 + 100} 条记录")
        print_flush(f"  - 处理进度: {(i+1)*10}%")

        # 模拟一些处理时间
        time.sleep(3)

        if i == 4:
            print_flush("  - 中间检查点：数据处理正常")

        if i == 7:
            print_flush("  - 警告：发现异常数据，已自动修复")

    print_flush("数据处理完成！")
    print_flush("=" * 50)

    # 模拟结果输出
    print_flush("生成报告...")
    time.sleep(2)
    print_flush("报告生成完成")

    print_flush("清理临时文件...")
    time.sleep(1)
    print_flush("清理完成")

    print_flush("=" * 50)
    print_flush(f"[{datetime.now()}] 任务执行完成")
    print_flush("总计处理：1000 条记录")
    print_flush("执行状态：成功")

if __name__ == "__main__":
    main()
