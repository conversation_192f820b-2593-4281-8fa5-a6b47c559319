#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试相对路径脚本
验证任务调度器是否正确处理相对路径
"""

import os
import sys
from datetime import datetime

def main():
    print(f"[{datetime.now()}] 测试相对路径脚本启动")
    
    # 显示当前工作目录
    current_dir = os.getcwd()
    print(f"当前工作目录: {current_dir}")
    
    # 显示脚本路径
    script_path = os.path.abspath(__file__)
    print(f"脚本路径: {script_path}")
    
    # 显示Python路径
    print(f"Python路径: {sys.executable}")
    
    # 显示PYTHONPATH
    pythonpath = os.environ.get('PYTHONPATH', '未设置')
    print(f"PYTHONPATH: {pythonpath}")
    
    # 测试导入项目模块
    try:
        from src.utils.ad_database import AdDatabase
        print("✅ 成功导入 AdDatabase 模块")
        
        # 测试数据库连接
        db = AdDatabase()
        print(f"✅ 数据库路径: {db.db_path}")
        print(f"✅ 数据库文件存在: {os.path.exists(db.db_path)}")
        
    except ImportError as e:
        print(f"❌ 导入模块失败: {e}")
    except Exception as e:
        print(f"❌ 其他错误: {e}")
    
    # 显示环境变量
    task_name = os.environ.get('TASK_NAME', '未设置')
    print(f"任务名称环境变量: {task_name}")
    
    print(f"[{datetime.now()}] 测试相对路径脚本完成")

if __name__ == "__main__":
    main()
