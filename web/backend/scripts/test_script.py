#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
示例测试脚本
用于演示任务调度系统
"""

import os
import sys
import time
import random
from datetime import datetime

def main():
    """主函数"""
    print(f"[{datetime.now()}] 测试脚本开始执行")
    print(f"当前工作目录: {os.getcwd()}")
    print(f"脚本路径: {__file__}")
    
    # 获取环境变量
    task_name = os.environ.get('TASK_NAME', '未知任务')
    print(f"任务名称: {task_name}")
    
    # 模拟一些工作
    work_time = random.randint(1, 5)
    print(f"模拟工作 {work_time} 秒...")
    
    for i in range(work_time):
        time.sleep(1)
        print(f"工作进度: {i+1}/{work_time}")
    
    # 随机决定成功或失败（80%成功率）
    if random.random() < 0.8:
        print("✅ 任务执行成功!")
        sys.exit(0)
    else:
        print("❌ 任务执行失败!")
        sys.exit(1)

if __name__ == "__main__":
    main()
