#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
数据备份任务示例
模拟数据库备份操作
"""

import os
import sys
import time
import json
import shutil
from datetime import datetime
from pathlib import Path

def main():
    """主函数"""
    print(f"[{datetime.now()}] 开始执行数据备份任务")
    
    # 获取环境变量
    backup_type = os.environ.get('BACKUP_TYPE', 'full')
    backup_dir = os.environ.get('BACKUP_DIR', '/tmp/backups')
    retention_days = int(os.environ.get('RETENTION_DAYS', '7'))
    
    print(f"备份类型: {backup_type}")
    print(f"备份目录: {backup_dir}")
    print(f"保留天数: {retention_days}")
    
    try:
        # 创建备份目录
        backup_path = Path(backup_dir)
        backup_path.mkdir(parents=True, exist_ok=True)
        
        # 生成备份文件名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_filename = f"backup_{backup_type}_{timestamp}.json"
        backup_file = backup_path / backup_filename
        
        # 模拟备份数据
        print("正在生成备份数据...")
        backup_data = {
            'backup_type': backup_type,
            'timestamp': timestamp,
            'created_at': datetime.now().isoformat(),
            'data': {
                'users': ['admin', 'editor', 'viewer'],
                'configs': {'setting1': 'value1', 'setting2': 'value2'},
                'stats': {'total_records': 1000, 'active_users': 50}
            }
        }
        
        # 模拟备份过程
        for i in range(5):
            time.sleep(0.5)
            print(f"备份进度: {(i+1)*20}%")
        
        # 写入备份文件
        with open(backup_file, 'w', encoding='utf-8') as f:
            json.dump(backup_data, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 备份文件已创建: {backup_file}")
        
        # 清理旧备份文件
        print("正在清理旧备份文件...")
        cleanup_old_backups(backup_path, retention_days)
        
        print(f"✅ 数据备份任务完成，耗时: {time.time():.2f}秒")
        
    except Exception as e:
        print(f"❌ 备份任务失败: {e}")
        sys.exit(1)

def cleanup_old_backups(backup_dir: Path, retention_days: int):
    """清理旧的备份文件"""
    cutoff_time = time.time() - (retention_days * 24 * 3600)
    
    cleaned_count = 0
    for backup_file in backup_dir.glob("backup_*.json"):
        if backup_file.stat().st_mtime < cutoff_time:
            backup_file.unlink()
            cleaned_count += 1
            print(f"删除旧备份: {backup_file.name}")
    
    if cleaned_count == 0:
        print("没有需要清理的旧备份文件")
    else:
        print(f"已清理 {cleaned_count} 个旧备份文件")

if __name__ == "__main__":
    main()
