#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
初始化创意数据库脚本
解决 creative 表不存在的问题
"""

import sqlite3
import os

# 数据库文件路径
DB_PATH = "creative_data.db"

def init_creative_database():
    """初始化创意数据库，创建 creative 表"""
    print(f"正在初始化数据库: {DB_PATH}")
    
    # 检查数据库文件是否存在
    if os.path.exists(DB_PATH):
        print(f"数据库文件已存在: {DB_PATH}")
    else:
        print(f"创建新的数据库文件: {DB_PATH}")
    
    conn = sqlite3.connect(DB_PATH)
    c = conn.cursor()
    
    # 创建 creative 表
    c.execute('''
        CREATE TABLE IF NOT EXISTS creative (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            uid TEXT,
            accountId INTEGER,
            adgroupId INTEGER,
            adgroupName TEXT,
            dynamicCreativeId INTEGER,
            configuredStatus TEXT,
            brandName TEXT,
            imageId INTEGER,
            dynamicCreativeName TEXT,
            pageType TEXT,
            systemStatusCn TEXT,
            miniGameAdMonetizationRoi TEXT,
            miniGameIncomeRoi1 TEXT,
            cost TEXT,
            amount TEXT,
            miniGameFirstDayAdMonetizationAmount TEXT,
            miniGameAdMonetizationAmount TEXT,
            miniGameRegisterCost TEXT,
            miniGameRetentionD1Rate TEXT,
            creativeTemplateId INTEGER,
            cpc TEXT,
            videoId INTEGER,
            type TEXT,
            content TEXT,
            url TEXT,
            created_at TEXT,
            raw_json TEXT,
            filter_version TEXT,
            filter_name TEXT,
            config_uids TEXT,
            group_name TEXT,
            fileId INTEGER,
            fileId_data TEXT,
            flag INTEGER,
            saved_to_library INTEGER,
            saved_at TEXT,
            used_by_uid TEXT
        )
    ''')
    
    # 创建 fileid_usage 表
    c.execute('''
        CREATE TABLE IF NOT EXISTS fileid_usage (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            group_name TEXT NOT NULL,
            file_id INTEGER NOT NULL,
            uid INTEGER NOT NULL,
            used_at TEXT NOT NULL,
            UNIQUE(group_name, file_id, uid)
        )
    ''')
    
    # 创建索引
    try:
        c.execute('CREATE UNIQUE INDEX IF NOT EXISTS idx_group_dynamic ON creative(group_name, dynamicCreativeId)')
    except sqlite3.OperationalError:
        pass  # 索引已存在
    
    conn.commit()
    
    # 检查表是否创建成功
    c.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='creative'")
    if c.fetchone():
        print("✅ creative 表创建成功")
    else:
        print("❌ creative 表创建失败")
    
    c.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='fileid_usage'")
    if c.fetchone():
        print("✅ fileid_usage 表创建成功")
    else:
        print("❌ fileid_usage 表创建失败")
    
    # 显示表结构
    c.execute("PRAGMA table_info(creative)")
    columns = c.fetchall()
    print(f"📋 creative 表包含 {len(columns)} 个字段:")
    for col in columns:
        print(f"  - {col[1]} ({col[2]})")
    
    conn.close()
    print("🎉 数据库初始化完成!")

if __name__ == "__main__":
    init_creative_database()
