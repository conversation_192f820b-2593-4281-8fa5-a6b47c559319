#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import urllib.request
import urllib.parse
import urllib.error
import json
from datetime import datetime
from web.backend.scripts.投放自动化.creative_db import DB_PATH
import requests
import time
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
import random

def get_authorization():
    """动态获取authorization token"""
    try:
        auth_headers = {
            'Authorization': 'your_secret_token_123'
        }
        auth_response = requests.get('http://***************:5000/get/tfpt', headers=auth_headers)
        if auth_response.status_code == 200:
            return auth_response.text.strip()
        else:
            raise Exception(f"获取Authorization失败，状态码: {auth_response.status_code}")
    except Exception as e:
        print(f"获取authorization失败: {e}")
        print("程序无法继续执行，请检查token服务是否正常")
        import sys
        sys.exit(1)

def get_all_url_data():
    """获取数据库中所有已有URL的创意数据（排除上传失败的记录）"""
    conn = sqlite3.connect(DB_PATH)
    c = conn.cursor()
    c.execute('SELECT url, brandName, dynamicCreativeId FROM creative WHERE url IS NOT NULL AND url != "" AND url != "上传失败"')
    rows = c.fetchall()
    conn.close()
    return rows

def reset_save_status():
    """重置所有记录的保存状态"""
    conn = sqlite3.connect(DB_PATH)
    c = conn.cursor()
    c.execute('UPDATE creative SET saved_to_library = 0, saved_at = NULL WHERE url IS NOT NULL AND url != "" AND url != "上传失败"')
    affected_rows = c.rowcount
    conn.commit()
    conn.close()
    print(f"已重置 {affected_rows} 条记录的保存状态")
    return affected_rows

def update_creative_save_status(dynamic_creative_id, saved=True):
    """更新数据库中创意的保存状态和时间"""
    conn = sqlite3.connect(DB_PATH)
    c = conn.cursor()
    save_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S") if saved else None
    c.execute('UPDATE creative SET saved_to_library = ?, saved_at = ? WHERE dynamicCreativeId = ?', 
              (1 if saved else 0, save_time, dynamic_creative_id))
    conn.commit()
    conn.close()
    if saved:
        print(f"已标记创意 {dynamic_creative_id} 为已保存到素材库")
    else:
        print(f"已标记创意 {dynamic_creative_id} 为未保存")

def format_url_data(url, brand_name):
    """格式化URL数据为API所需格式"""
    # 清理文件名
    clean_filename = "".join(c for c in brand_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
    
    # 根据URL判断文件类型
    if url.lower().endswith('.png'):
        file_ext = '.png'
    elif url.lower().endswith('.jpg') or url.lower().endswith('.jpeg'):
        file_ext = '.jpg'
    elif url.lower().endswith('.gif'):
        file_ext = '.gif'
    elif url.lower().endswith('.webp'):
        file_ext = '.webp'
    else:
        file_ext = '.jpg'  # 默认
    
    return {
        "url": url,
        "originalFilename": f"{clean_filename}{file_ext}",
        "width": 512,
        "height": 512,
        "fileType": 1,
        "groupId": 416,
        "type": 3
    }

def save_to_material_library_with_retry(urls_data, max_retries=3):
    """带重试机制的批量保存到素材库"""
    for attempt in range(max_retries):
        try:
            result = save_to_material_library(urls_data)
            if result:
                return True
            else:
                if attempt < max_retries - 1:
                    wait_time = (2 ** attempt) + random.uniform(0, 1)
                    print(f"第 {attempt + 1} 次保存失败，{wait_time:.1f}秒后重试...")
                    time.sleep(wait_time)
                else:
                    print(f"经过 {max_retries} 次重试后仍然失败")
                    return False
        except Exception as e:
            if attempt < max_retries - 1:
                wait_time = (2 ** attempt) + random.uniform(0, 1)
                print(f"第 {attempt + 1} 次保存异常: {e}，{wait_time:.1f}秒后重试...")
                time.sleep(wait_time)
            else:
                print(f"经过 {max_retries} 次重试后仍然异常: {e}")
                return False
    return False

def save_to_material_library(urls_data):
    """批量保存到素材库"""
    save_url = "http://game.raisedsun.com/prod-api/system/info/saveBatch"
    
    try:
        # 构建请求数据
        request_data = {
            "urls": urls_data,
            "labelIds": [],
            "labelName": None
        }
        
        # 转换为JSON字符串
        json_data = json.dumps(request_data, ensure_ascii=False)
        
        # 创建请求
        req = urllib.request.Request(save_url, data=json_data.encode('utf-8'))
        
        # 获取动态authorization
        authorization = get_authorization()
        # 提取token（去掉Bearer前缀）
        token = authorization.replace('Bearer ', '') if authorization.startswith('Bearer ') else authorization
        
        # 设置请求头
        req.add_header('Accept', 'application/json, text/plain, */*')
        req.add_header('Accept-Language', 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6')
        req.add_header('Authorization', authorization)
        req.add_header('Content-Type', 'application/json;charset=UTF-8')
        req.add_header('Origin', 'http://game.raisedsun.com')
        req.add_header('Referer', 'http://game.raisedsun.com/ad/material')
        req.add_header('User-Agent', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0')
        req.add_header('Cookie', f'Admin-Token={token}; sidebarStatus=1')
        
        print(f"[线程{threading.current_thread().name}] 正在保存 {len(urls_data)} 条数据到素材库...")
        
        # 发送请求
        with urllib.request.urlopen(req, timeout=30) as response:
            response_data = response.read().decode('utf-8')
            status_code = response.getcode()
            
            print(f"[线程{threading.current_thread().name}] 保存响应状态码: {status_code}")
            
            if status_code == 200:
                try:
                    response_json = json.loads(response_data)
                    if response_json.get('code') == 200:
                        print(f"[线程{threading.current_thread().name}] 成功保存 {len(urls_data)} 条数据到素材库")
                        return True
                    else:
                        print(f"[线程{threading.current_thread().name}] 保存失败: {response_json.get('msg', '未知错误')}")
                        return False
                except json.JSONDecodeError:
                    print(f"[线程{threading.current_thread().name}] 保存响应格式异常: {response_data}")
                    return False
            else:
                print(f"[线程{threading.current_thread().name}] 保存失败: {status_code} - {response_data}")
                return False
                
    except Exception as e:
        print(f"[线程{threading.current_thread().name}] 保存到素材库时发生错误: {e}")
        return False

def process_batch_with_retry(batch_data, batch_num):
    """带重试机制的批次处理函数"""
    print(f"\n[线程{threading.current_thread().name}] 处理第 {batch_num} 批数据 ({len(batch_data)} 条)")
    
    # 格式化数据
    urls_data = []
    batch_creative_ids = []
    for url, brand_name, creative_id in batch_data:
        formatted_data = format_url_data(url, brand_name)
        urls_data.append(formatted_data)
        batch_creative_ids.append(creative_id)
        print(f"[线程{threading.current_thread().name}] 准备保存: {brand_name} -> {url}")
    
    # 保存到素材库（带重试）
    if save_to_material_library_with_retry(urls_data):
        # 更新数据库状态
        for creative_id in batch_creative_ids:
            update_creative_save_status(creative_id, True)
        print(f"[线程{threading.current_thread().name}] 第 {batch_num} 批数据保存成功")
        return len(batch_data), 0
    else:
        print(f"[线程{threading.current_thread().name}] 第 {batch_num} 批数据保存失败")
        return 0, len(batch_data)

def resave_all_urls_to_library():
    """重新保存所有URL到素材库（支持并发处理）"""
    print("开始重新保存所有URL到素材库...")
    
    # 重置所有记录的保存状态
    reset_count = reset_save_status()
    if reset_count == 0:
        print("没有找到需要重新保存的数据")
        return 0, 0
    
    # 获取所有URL数据
    creative_data = get_all_url_data()
    
    if not creative_data:
        print("没有找到需要保存到素材库的数据")
        return 0, 0
    
    print(f"找到 {len(creative_data)} 条需要重新保存的数据")
    
    success_count = 0
    fail_count = 0
    batch_size = 50  # 每批50条，适合并发处理
    
    # 分批数据
    batches = []
    for i in range(0, len(creative_data), batch_size):
        batch_data = creative_data[i:i + batch_size]
        batches.append((batch_data, i//batch_size + 1))
    
    # 使用线程池进行并发处理
    max_workers = min(3, len(batches))  # 最大3个线程，避免对服务器压力过大
    print(f"使用 {max_workers} 个线程并发处理 {len(batches)} 个批次...")
    
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有批次处理任务
        future_to_batch = {}
        for batch_data, batch_num in batches:
            future = executor.submit(process_batch_with_retry, batch_data, batch_num)
            future_to_batch[future] = batch_num
        
        # 处理完成的任务
        for future in as_completed(future_to_batch):
            batch_num = future_to_batch[future]
            try:
                batch_success, batch_fail = future.result()
                success_count += batch_success
                fail_count += batch_fail
                print(f"✓ 批次 {batch_num} 处理完成: 成功 {batch_success}, 失败 {batch_fail}")
            except Exception as e:
                print(f"✗ 批次 {batch_num} 处理异常: {e}")
                # 假设该批次全部失败
                batch_data, _ = batches[batch_num - 1]
                fail_count += len(batch_data)
    
    return success_count, fail_count

def get_save_statistics():
    """获取保存统计信息"""
    conn = sqlite3.connect(DB_PATH)
    c = conn.cursor()
    
    # 总URL数量（排除上传失败）
    c.execute('SELECT COUNT(*) FROM creative WHERE url IS NOT NULL AND url != "" AND url != "上传失败"')
    total_urls = c.fetchone()[0]
    
    # 已保存数量
    c.execute('SELECT COUNT(*) FROM creative WHERE url IS NOT NULL AND url != "" AND url != "上传失败" AND saved_to_library = 1')
    saved_count = c.fetchone()[0]
    
    # 未保存数量
    c.execute('SELECT COUNT(*) FROM creative WHERE url IS NOT NULL AND url != "" AND url != "上传失败" AND (saved_to_library IS NULL OR saved_to_library = 0)')
    unsaved_count = c.fetchone()[0]
    
    conn.close()
    return total_urls, saved_count, unsaved_count

def main():
    """主函数"""
    print("=" * 60)
    print("重新保存所有URL到素材库脚本")
    print("=" * 60)
    
    # 显示当前统计信息
    total_urls, saved_count, unsaved_count = get_save_statistics()
    print(f"\n当前数据库统计:")
    print(f"总URL数量: {total_urls}")
    print(f"已保存数量: {saved_count}")
    print(f"未保存数量: {unsaved_count}")
    
    if total_urls == 0:
        print("\n数据库中没有找到有效的URL数据")
        return
    
    # 确认是否继续
    print(f"\n即将重新保存所有 {total_urls} 条URL到素材库")
    print("注意: 这将重置所有记录的保存状态并重新保存")
    
    # 开始重新保存
    print("\n开始执行重新保存任务...")
    start_time = time.time()
    
    success_count, fail_count = resave_all_urls_to_library()
    
    end_time = time.time()
    duration = end_time - start_time
    
    # 显示最终统计
    print("\n" + "=" * 60)
    print("重新保存任务完成！")
    print("=" * 60)
    print(f"处理时间: {duration:.2f} 秒")
    print(f"成功保存: {success_count} 条")
    print(f"保存失败: {fail_count} 条")
    print(f"成功率: {(success_count / (success_count + fail_count) * 100):.1f}%" if (success_count + fail_count) > 0 else "0.0%")
    
    # 显示更新后的统计信息
    total_urls, saved_count, unsaved_count = get_save_statistics()
    print(f"\n更新后数据库统计:")
    print(f"总URL数量: {total_urls}")
    print(f"已保存数量: {saved_count}")
    print(f"未保存数量: {unsaved_count}")
    
    print(f"\n脚本特点:")
    print(f"- 支持并发处理，提高保存效率")
    print(f"- 智能重试机制，提高成功率")
    print(f"- 自动重置保存状态，确保完整重新保存")
    print(f"- 详细的进度跟踪和统计信息")
    print(f"- 线程安全的数据库操作")

if __name__ == "__main__":
    main()