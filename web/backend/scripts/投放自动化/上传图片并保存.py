#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import urllib.request
import urllib.parse
import urllib.error
import json
import os
from datetime import datetime
from creative_db import DB_PATH
import requests
import time
import random
from PIL import Image
import io

# 确保使用正确的数据库路径
script_dir = os.path.dirname(os.path.abspath(__file__))
DB_PATH_ABSOLUTE = os.path.join(script_dir, "creative_data.db")

print(f"脚本目录: {script_dir}")
print(f"数据库路径: {DB_PATH_ABSOLUTE}")
print(f"数据库文件存在: {os.path.exists(DB_PATH_ABSOLUTE)}")

def get_creative_data():
    """从数据库获取创意数据，排除已有url数据的记录"""
    print(f"正在连接数据库: {DB_PATH_ABSOLUTE}")
    print(f"数据库文件存在: {os.path.exists(DB_PATH_ABSOLUTE)}")

    conn = sqlite3.connect(DB_PATH_ABSOLUTE)
    c = conn.cursor()

    # 检查表是否存在
    c.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='creative'")
    table_exists = c.fetchone()
    print(f"creative表存在: {table_exists is not None}")

    if not table_exists:
        print("❌ creative表不存在，正在创建...")
        # 如果表不存在，创建它
        from creative_db import init_db
        conn.close()
        init_db()
        conn = sqlite3.connect(DB_PATH_ABSOLUTE)
        c = conn.cursor()

    c.execute('SELECT accountId, imageId, brandName, dynamicCreativeId FROM creative WHERE imageId IS NOT NULL AND (url IS NULL OR url = "" OR url = "上传失败")')
    rows = c.fetchall()
    print(f"找到 {len(rows)} 条需要处理的创意数据")
    conn.close()
    return rows

def update_creative_url(dynamic_creative_id, url):
    """更新数据库中创意的url字段"""
    conn = sqlite3.connect(DB_PATH_ABSOLUTE)
    c = conn.cursor()
    c.execute('UPDATE creative SET url = ? WHERE dynamicCreativeId = ?', (url, dynamic_creative_id))
    conn.commit()
    conn.close()
    print(f"已更新创意 {dynamic_creative_id} 的URL: {url}")

def update_creative_save_status(dynamic_creative_id, saved=True):
    """更新数据库中创意的保存状态和时间"""
    conn = sqlite3.connect(DB_PATH_ABSOLUTE)
    c = conn.cursor()
    save_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S") if saved else None
    c.execute('UPDATE creative SET saved_to_library = ?, saved_at = ? WHERE dynamicCreativeId = ?',
              (1 if saved else 0, save_time, dynamic_creative_id))
    conn.commit()
    conn.close()
    if saved:
        print(f"已标记创意 {dynamic_creative_id} 为已保存到素材库")
    else:
        print(f"已标记创意 {dynamic_creative_id} 为未保存")

def get_authorization():
    """动态获取authorization token"""
    try:
        auth_headers = {
            'Authorization': 'your_secret_token_123'
        }
        auth_response = requests.get('http://***************:5000/get/tfpt', headers=auth_headers)
        if auth_response.status_code == 200:
            return auth_response.text.strip()
        else:
            raise Exception(f"获取Authorization失败，状态码: {auth_response.status_code}")
    except Exception as e:
        print(f"获取authorization失败: {e}")
        print("程序无法继续执行，请检查token服务是否正常")
        import sys
        sys.exit(1)

        

def get_creative_data_for_save():
    """获取已有URL但未保存到素材库的创意数据，排除上传失败和分辨率不符合要求的记录"""
    conn = sqlite3.connect(DB_PATH_ABSOLUTE)
    c = conn.cursor()
    c.execute('SELECT url, brandName, dynamicCreativeId FROM creative WHERE url IS NOT NULL AND url != "" AND url != "上传失败" AND url != "分辨率不符合要求" AND (saved_to_library IS NULL OR saved_to_library = 0)')
    rows = c.fetchall()
    conn.close()
    return rows

def download_and_upload_image_with_retry(account_id, image_id, brand_name, creative_id, max_retries=3):
    """带重试机制的下载并上传图片函数"""
    for attempt in range(max_retries):
        try:
            result = download_and_upload_image(account_id, image_id, brand_name, creative_id)
            if result:  # 成功则返回结果
                return result
            else:
                if attempt < max_retries - 1:
                    wait_time = (2 ** attempt) + random.uniform(0, 1)  # 指数退避 + 随机抖动
                    print(f"创意 {creative_id} 第 {attempt + 1} 次上传失败，{wait_time:.1f}秒后重试...")
                    time.sleep(wait_time)
                else:
                    print(f"创意 {creative_id} 经过 {max_retries} 次重试后仍然失败")
                    return False
        except Exception as e:
            if attempt < max_retries - 1:
                wait_time = (2 ** attempt) + random.uniform(0, 1)
                print(f"创意 {creative_id} 第 {attempt + 1} 次上传异常: {e}，{wait_time:.1f}秒后重试...")
                time.sleep(wait_time)
            else:
                print(f"创意 {creative_id} 经过 {max_retries} 次重试后仍然异常: {e}")
                return False
    return False

def clean_debug_images():
    """清理旧的调试图片"""
    try:
        debug_dir = os.path.join(script_dir, "debug_images")
        if os.path.exists(debug_dir):
            import shutil
            shutil.rmtree(debug_dir)
            print("已清理旧的调试图片")
    except Exception as e:
        print(f"清理调试图片失败: {e}")

def check_image_resolution(image_data, brand_name, creative_id):
    """检查图片分辨率是否为512x512"""
    try:
        # 使用PIL检查图片分辨率
        image = Image.open(io.BytesIO(image_data))
        width, height = image.size

        print(f"图片分辨率: {brand_name} (ID: {creative_id}) - {width}x{height}")

        if width == 512 and height == 512:
            print(f"✅ 图片分辨率符合要求: {width}x{height}")
            return True
        else:
            print(f"❌ 图片分辨率不符合要求: {width}x{height} (需要512x512)")
            return False

    except Exception as e:
        print(f"检查图片分辨率失败: {brand_name} (ID: {creative_id}) - {e}")
        return False

def save_image_for_debug(image_data, brand_name, creative_id, file_ext):
    """保存图片数据到本地文件用于调试"""
    try:
        # 创建debug目录
        debug_dir = os.path.join(script_dir, "debug_images")
        if not os.path.exists(debug_dir):
            os.makedirs(debug_dir)

        # 清理文件名，移除特殊字符
        safe_brand_name = "".join(c for c in brand_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
        if not safe_brand_name:
            safe_brand_name = "unknown"

        # 构建文件名
        filename = f"{safe_brand_name}_{creative_id}.{file_ext}"
        filepath = os.path.join(debug_dir, filename)

        # 保存图片数据
        with open(filepath, 'wb') as f:
            f.write(image_data)

        print(f"调试图片已保存: {filepath}")

    except Exception as e:
        print(f"保存调试图片失败: {e}")

def download_and_upload_image(account_id, image_id, brand_name, creative_id):
    """直接下载图片并上传到服务器，不保存到本地"""
    # 组装图片URL
    image_url = f"http://game.raisedsun.com/prod-api/ad/image/show/{image_id}/{account_id}/image"
    upload_url = "http://game.raisedsun.com/prod-api/common/upload"
    
    try:
        print(f"正在下载图片: {brand_name} (ID: {creative_id})")
        
        # 下载图片到内存
        req = urllib.request.Request(image_url)
        with urllib.request.urlopen(req, timeout=30) as response:
            # 获取响应头中的content-type
            content_type = response.getheader('Content-Type', '').lower()
            
            # 根据content-type确定文件扩展名和MIME类型
            if 'image/jpeg' in content_type or 'image/jpg' in content_type:
                file_ext = '.jpg'
                mime_type = 'image/jpeg'
            elif 'image/png' in content_type:
                file_ext = '.png'
                mime_type = 'image/png'
            elif 'image/gif' in content_type:
                file_ext = '.gif'
                mime_type = 'image/gif'
            elif 'image/webp' in content_type:
                file_ext = '.webp'
                mime_type = 'image/webp'
            elif 'image/bmp' in content_type:
                file_ext = '.bmp'
                mime_type = 'image/bmp'
            elif 'image/svg' in content_type:
                file_ext = '.svg'
                mime_type = 'image/svg+xml'
            else:
                # 默认使用png，如果无法识别content-type
                file_ext = '.png'
                mime_type = 'image/png'
            
            # 读取图片数据到内存
            image_data = response.read()
            print(f"图片下载完成: {brand_name}, 大小: {len(image_data)} 字节")

            # 保存图片数据到本地文件用于调试
            save_image_for_debug(image_data, brand_name, creative_id, file_ext)

            # 检查图片分辨率
            if not check_image_resolution(image_data, brand_name, creative_id):
                print(f"跳过上传: {brand_name} (ID: {creative_id}) - 分辨率不符合要求")
                return "分辨率不符合要求"

        # 直接上传图片数据
        return upload_image_data(image_data, brand_name, file_ext, mime_type, creative_id)
        
    except Exception as e:
        print(f"下载图片失败: {brand_name} - {e}")
        return False

def upload_image_data(image_data, brand_name, file_ext, mime_type, creative_id):
    """上传图片数据到服务器"""
    upload_url = "http://game.raisedsun.com/prod-api/common/upload"
    
    try:
        # 构建文件名
        base_filename = "".join(c for c in brand_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
        filename_with_ext = f"{base_filename}{file_ext}"
        
        # 构建multipart/form-data
        boundary = '----WebKitFormBoundaryxjE6m7x73WX1fJ6s'
        
        # 构建请求体
        body_parts = []
        body_parts.append(f'--{boundary}'.encode())
        body_parts.append(f'Content-Disposition: form-data; name="file"; filename="{filename_with_ext}"'.encode())
        body_parts.append(f'Content-Type: {mime_type}'.encode())
        body_parts.append(b'')
        body_parts.append(image_data)
        body_parts.append(f'--{boundary}--'.encode())
        
        body = b'\r\n'.join(body_parts)
        
        # 创建请求
        req = urllib.request.Request(upload_url, data=body)
        
        # 获取动态authorization
        authorization = get_authorization()
        # 提取token（去掉Bearer前缀）
        token = authorization.replace('Bearer ', '') if authorization.startswith('Bearer ') else authorization
        
        # 设置请求头
        req.add_header('Accept', '*/*')
        req.add_header('Accept-Language', 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6')
        req.add_header('Authorization', authorization)
        req.add_header('Content-Type', f'multipart/form-data; boundary={boundary}')
        req.add_header('FileType', '1')
        req.add_header('Origin', 'http://game.raisedsun.com')
        req.add_header('Referer', 'http://game.raisedsun.com/ad/material')
        req.add_header('Type', '3')
        req.add_header('User-Agent', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********')
        req.add_header('Cookie', f'Admin-Token={token}; sidebarStatus=1')
        
        print(f"正在上传图片: {brand_name} (ID: {creative_id}, 大小: {len(image_data)} 字节)")
        
        # 发送请求
        with urllib.request.urlopen(req, timeout=30) as response:
            response_data = response.read().decode('utf-8')
            status_code = response.getcode()
            
            if status_code == 200:
                try:
                    # 解析响应JSON获取URL
                    response_json = json.loads(response_data)
                    if response_json.get('code') == 200 and 'url' in response_json:
                        uploaded_url = response_json.get('url')
                        if uploaded_url:
                            print(f"图片上传成功: {brand_name} (ID: {creative_id}), URL: {uploaded_url}")
                            return uploaded_url
                    print(f"图片上传成功但未获取到URL: {brand_name} (ID: {creative_id})")
                    return None
                except json.JSONDecodeError:
                    print(f"图片上传成功但响应格式异常: {brand_name} (ID: {creative_id})")
                    return None
            else:
                print(f"图片上传失败: {brand_name} (ID: {creative_id}) - {status_code}")
                return None

    except Exception as e:
        print(f"上传图片时发生错误: {brand_name} (ID: {creative_id}) - {e}")
        return False

def format_url_data(url, brand_name):
    """格式化URL数据为API所需格式"""
    # 清理文件名
    clean_filename = "".join(c for c in brand_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
    
    # 根据URL判断文件类型
    if url.lower().endswith('.png'):
        file_ext = '.png'
    elif url.lower().endswith('.jpg') or url.lower().endswith('.jpeg'):
        file_ext = '.jpg'
    elif url.lower().endswith('.gif'):
        file_ext = '.gif'
    elif url.lower().endswith('.webp'):
        file_ext = '.webp'
    else:
        file_ext = '.jpg'  # 默认
    
    return {
        "url": url,
        "originalFilename": f"{clean_filename}{file_ext}",
        "width": 512,
        "height": 512,
        "fileType": 1,
        "groupId": 416,
        "type": 3
    }

def save_to_material_library(urls_data):
    """批量保存到素材库"""
    save_url = "http://game.raisedsun.com/prod-api/system/info/saveBatch"
    
    try:
        # 构建请求数据
        request_data = {
            "urls": urls_data,
            "labelIds": [],
            "labelName": None
        }
        
        # 转换为JSON字符串
        json_data = json.dumps(request_data, ensure_ascii=False)
        
        # 创建请求
        req = urllib.request.Request(save_url, data=json_data.encode('utf-8'))
        
        # 获取动态authorization
        authorization = get_authorization()
        # 提取token（去掉Bearer前缀）
        token = authorization.replace('Bearer ', '') if authorization.startswith('Bearer ') else authorization
        
        # 设置请求头
        req.add_header('Accept', 'application/json, text/plain, */*')
        req.add_header('Accept-Language', 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6')
        req.add_header('Authorization', authorization)
        req.add_header('Content-Type', 'application/json;charset=UTF-8')
        req.add_header('Origin', 'http://game.raisedsun.com')
        req.add_header('Referer', 'http://game.raisedsun.com/ad/material')
        req.add_header('User-Agent', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********')
        req.add_header('Cookie', f'Admin-Token={token}; sidebarStatus=1')
        
        print(f"正在保存 {len(urls_data)} 条数据到素材库...")
        
        # 发送请求
        with urllib.request.urlopen(req, timeout=30) as response:
            response_data = response.read().decode('utf-8')
            status_code = response.getcode()
            
            print(f"保存响应状态码: {status_code}")
            print(f"保存响应内容: {response_data}")
            
            if status_code == 200:
                try:
                    response_json = json.loads(response_data)
                    if response_json.get('code') == 200:
                        print(f"成功保存 {len(urls_data)} 条数据到素材库")
                        return True
                    else:
                        print(f"保存失败: {response_json.get('msg', '未知错误')}")
                        return False
                except json.JSONDecodeError:
                    print(f"保存响应格式异常: {response_data}")
                    return False
            else:
                print(f"保存失败: {status_code} - {response_data}")
                return False
                
    except Exception as e:
        print(f"保存到素材库时发生错误: {e}")
        return False

def save_uploaded_images_to_library():
    """将已上传的图片保存到素材库"""
    print("\n开始将已上传的图片保存到素材库...")
    
    # 获取已有URL但未保存的创意数据
    creative_data = get_creative_data_for_save()
    
    if not creative_data:
        print("没有找到需要保存到素材库的数据")
        return 0, 0
    
    print(f"找到 {len(creative_data)} 条需要保存的数据")
    
    success_count = 0
    fail_count = 0
    batch_size = 100  # 每批最多100条
    
    # 分批处理数据
    for i in range(0, len(creative_data), batch_size):
        batch_data = creative_data[i:i + batch_size]
        print(f"\n处理第 {i//batch_size + 1} 批数据 ({len(batch_data)} 条)")
        
        # 格式化数据
        urls_data = []
        batch_creative_ids = []
        for url, brand_name, creative_id in batch_data:
            formatted_data = format_url_data(url, brand_name)
            urls_data.append(formatted_data)
            batch_creative_ids.append(creative_id)
            print(f"准备保存: {brand_name} -> {url}")
        
        # 保存到素材库
        if save_to_material_library(urls_data):
            # 更新数据库状态
            for creative_id in batch_creative_ids:
                update_creative_save_status(creative_id, True)
            success_count += len(batch_data)
            print(f"第 {i//batch_size + 1} 批数据保存成功")
        else:
            fail_count += len(batch_data)
            print(f"第 {i//batch_size + 1} 批数据保存失败")
    
    return success_count, fail_count

def main():
    """主函数"""
    print("开始处理图片直接上传（保存调试副本）...")
    print(f"当前工作目录: {os.getcwd()}")
    print(f"脚本文件路径: {__file__}")
    print(f"脚本目录: {script_dir}")
    print(f"数据库路径: {DB_PATH_ABSOLUTE}")
    print(f"数据库文件存在: {os.path.exists(DB_PATH_ABSOLUTE)}")

    # 清理旧的调试图片
    clean_debug_images()

    # 获取数据库中的创意数据
    creative_data = get_creative_data()
    
    if not creative_data:
        print("数据库中没有找到包含imageId的创意数据")
        # 即使没有新的上传任务，也检查是否有已上传但未保存的图片
        save_success, save_fail = save_uploaded_images_to_library()
        if save_success > 0 or save_fail > 0:
            print(f"\n素材库保存结果: 成功 {save_success} 条, 失败 {save_fail} 条")
        return
    
    print(f"找到 {len(creative_data)} 条创意数据")
    
    success_count = 0
    fail_count = 0
   
    # 顺序处理上传任务
    print("开始顺序上传图片...")

    for i, (account_id, image_id, brand_name, creative_id) in enumerate(creative_data, 1):
        print(f"\n处理第 {i}/{len(creative_data)} 个创意...")
        try:
            uploaded_url = download_and_upload_image_with_retry(account_id, image_id, brand_name, creative_id)
            if uploaded_url and uploaded_url.startswith("http"):
                # 上传成功，更新数据库中的url字段
                update_creative_url(creative_id, uploaded_url)
                success_count += 1
                print(f"✓ 创意 {creative_id} ({brand_name}) 上传成功")
            elif uploaded_url == "分辨率不符合要求":
                # 分辨率不符合要求，标记为跳过
                update_creative_url(creative_id, "分辨率不符合要求")
                fail_count += 1
                print(f"⚠️ 创意 {creative_id} ({brand_name}) 分辨率不符合要求，已跳过")
            else:
                # 上传失败，将url设置为"上传失败"
                update_creative_url(creative_id, "上传失败")
                fail_count += 1
                print(f"✗ 创意 {creative_id} ({brand_name}) 上传失败")
        except Exception as e:
            # 上传异常，将url设置为"上传失败"
            update_creative_url(creative_id, "上传失败")
            fail_count += 1
            print(f"✗ 创意 {creative_id} ({brand_name}) 处理异常: {e}")

        # 在每个上传之间添加短暂延迟，避免对服务器造成压力
        if i < len(creative_data):
            time.sleep(0)
    
    print(f"\n图片上传处理完成！")
    print(f"成功: {success_count} 个")
    print(f"失败: {fail_count} 个")
    
    # 上传完成后，自动保存到素材库
    if success_count > 0:
        save_success, save_fail = save_uploaded_images_to_library()
        print(f"\n素材库保存结果: 成功 {save_success} 条, 失败 {save_fail} 条")
    else:
        # 即使本次没有新上传，也检查是否有之前上传但未保存的图片
        save_success, save_fail = save_uploaded_images_to_library()
        if save_success > 0 or save_fail > 0:
            print(f"\n素材库保存结果: 成功 {save_success} 条, 失败 {save_fail} 条")
    
    print(f"\n总结:")
    print(f"图片上传: 成功 {success_count} 个, 失败 {fail_count} 个")
    print(f"素材库保存: 成功 {save_success if 'save_success' in locals() else 0} 条, 失败 {save_fail if 'save_fail' in locals() else 0} 条")
    print(f"\n优势: 内存直接处理，自动保存到素材库，顺序处理避免服务器压力，保存调试图片便于问题排查，自动检查512x512分辨率")

if __name__ == "__main__":
    main()