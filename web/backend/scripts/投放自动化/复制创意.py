#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置查看器 - 通过API请求获取所有筛选配置并提取字段值
"""

import requests
import yaml
import os
import json
import sys
import random
import datetime
from typing import Dict, Any, List
import urllib3
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 配置文件路径
CONFIG_FILE = os.path.join(os.path.dirname(os.path.abspath(__file__)), "config.yaml")

def load_config() -> Dict[str, str]:
    """加载配置文件"""
    default_config = {"api_url": ""}
    
    if os.path.exists(CONFIG_FILE):
        try:
            with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f) or default_config
                if not config.get("api_url"):
                    print(f"错误: 配置文件中未设置api_url: {CONFIG_FILE}")
                    print("请编辑配置文件设置正确的API URL")
                    sys.exit(1)
                return config
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            sys.exit(1)
    else:
        # 创建默认配置文件
        try:
            with open(CONFIG_FILE, 'w', encoding='utf-8') as f:
                yaml.dump(default_config, f, default_flow_style=False, allow_unicode=True)
            print(f"已创建默认配置文件: {CONFIG_FILE}")
            print("请编辑配置文件设置正确的API URL后重新运行")
            sys.exit(1)
        except Exception as e:
            print(f"创建配置文件失败: {e}")
            sys.exit(1)
    
    return default_config

def get_all_filter_configs(api_url: str):
    """通过API请求获取所有筛选配置列表"""
    try:
        # 使用/filter-configs/all端点直接获取所有配置
        url = f"{api_url}/api/vue-element-admin/filter-configs/all"
        
        # 发送GET请求
        response = requests.get(url)
        
        # 返回JSON响应
        return response.json()
    except Exception as e:
        return {
            "code": 500,
            "message": f"获取筛选配置失败: {str(e)}",
            "data": None
        }

def print_config_values(configs: List[Dict[str, Any]]):
    """提取并打印配置中的各个字段值"""
    if not configs:
        print("没有找到任何配置")
        return
    
    print(f"\n找到 {len(configs)} 条配置记录:")
    print("=" * 80)
    
    # 打印各个配置
    for idx, config in enumerate(configs, 1):
        print(f"配置 #{idx}:")
        print(f"  ID: {config.get('id')}")
        print(f"  名称: {config.get('group_name')}")
        
        # 处理列表类型的字段
        filter_versions = config.get('filter_version', [])
        if filter_versions:
            print(f"  版本列表: {', '.join(str(v) for v in filter_versions)}")
        else:
            print("  版本列表: 空")
            
        filter_names = config.get('filter_name', [])
        if filter_names:
            print(f"  名称列表: {', '.join(str(n) for n in filter_names)}")
        else:
            print("  名称列表: 空")
            
        uids = config.get('uids', [])
        if uids:
            print(f"  UID列表: {', '.join(str(uid) for uid in uids)}")
        else:
            print("  UID列表: 空")

        # 处理标签列表
        label_names = config.get('labelName', [])
        if label_names:
            print(f"  标签列表: {', '.join(str(label) for label in label_names)}")
        else:
            print("  标签列表: 空")

        # 处理数值类型的字段
        print(f"  最小花费: {config.get('min_cost', 0)}")
        print(f"  最小ROI: {config.get('min_MonetizationRoi', 0)}")

        # 处理其他字段
        print(f"  应用ID: {config.get('appId', '')}")
        print(f"  状态: {'启用' if config.get('enabled') else '禁用'}")
        print(f"  创建时间: {config.get('created_at', '')}")
        print(f"  更新时间: {config.get('updated_at', '')}")
        print("-" * 80)

def get_auth_token():
    """获取Authorization值"""
    try:
        # 获取authorization
        auth_headers = {
            'Authorization': 'your_secret_token_123'
        }
        auth_response = requests.get('http://***************:5000/get/tfpt', headers=auth_headers)
        authorization = auth_response.text

        if auth_response.status_code == 200:
            # 确保返回的token包含Bearer前缀
            token = authorization.strip()
            if not token.startswith('Bearer '):
                token = f'Bearer {token}'
            return token
        else:
            print(f"获取Authorization失败，状态码: {auth_response.status_code}")
            return None
    except Exception as e:
        print(f"获取Authorization时发生错误: {str(e)}")
        return None

def get_game_info_by_label(label_name: str, token: str, group_id: int = 388):
    """根据标签名称获取素材信息，随机返回10个fileId"""
    import urllib.parse

    try:
        url = f'http://game.raisedsun.com/prod-api/system/info/list'

        # 构建参数，按照curl请求的顺序
        params: Dict[str, Any] = {
            'pageNum': 1,
            'pageSize': 500,  # 减少数量以便测试
            'fileType': 1,
            'type': 3
        }

        # 只有当标签不为空时才添加labelName参数
        if label_name.strip():
            # 直接使用原始标签名称，让requests自动处理编码
            params['labelName'] = label_name
        
        # 添加其他参数
        params['groupId'] = group_id
        params['isGroupType'] = 'true'
        headers = {
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
            'Authorization': token,
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
            'Referer': 'http://game.raisedsun.com/cy/uid/64178000',
            'Proxy-Connection': 'keep-alive'
        }
        cookies = {
            'Admin-Token': token.replace('Bearer ', ''),
            'sidebarStatus': '1'
        }

        response = requests.get(url, params=params, headers=headers, cookies=cookies, verify=False)
        
        if response.status_code == 200:
            result = response.json()
            total = result.get('total', 0)
            print(f"  ✅ 找到 {total} 条素材")
            return result
        else:
            print(f"  ❌ 查询失败 - 状态码: {response.status_code}")
            return None
    except Exception as e:
        print(f"  ❌ 素材库查询出错 - 错误信息: {str(e)}")
        return None

def extract_random_file_ids(game_info, limit=10):
    """从rows中随机提取fileId，最多提取limit个"""

    if isinstance(game_info, dict):
        # 直接从响应中获取rows数据
        rows = game_info.get('rows', [])

        # 收集所有有效的fileId
        candidates = []
        for row in rows:
            if 'fileId' in row and row.get('fileId'):
                file_id = row.get('fileId')
                candidates.append(file_id)

        # 随机选择limit个
        if candidates:
            # 如果candidates少于limit，则全部返回
            if len(candidates) <= limit:
                selected_file_ids = candidates
            else:
                # 随机选择limit个
                selected_file_ids = random.sample(candidates, limit)

            print(f"  选择 {len(selected_file_ids)} 个素材")
            return selected_file_ids
        else:
            print("  未找到可用素材")
            return []

    return []

def get_creative_list(uid, token):
    """获取创意列表，返回最近7天的数据"""
    try:
        # 计算最近7天的日期范围
        end_date = datetime.datetime.now()
        start_date = end_date - datetime.timedelta(days=7)
        
        # 格式化日期为YYYY-MM-DD
        start_date_str = start_date.strftime('%Y-%m-%d')
        end_date_str = end_date.strftime('%Y-%m-%d')
        
        url = 'http://game.raisedsun.com/prod-api/ad/account/creativeList'
        params = {
            'uid': uid,
            'pageNum': 1,
            'pageSize': 20,      # 限制为20条，因为我们只需要第一条数据
            'startDate': start_date_str,
            'endDate': end_date_str,
            'operationStatus': 3,
            'sortField': 'cost',
            'sortOrder': 'DESCENDING',
            'isEmpty': 'false'
        }
        
        headers = {
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
            'Authorization': token,
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
            'Referer': f'http://game.raisedsun.com/cy/uid/{uid}'
        }
        
        cookies = {
            'Admin-Token': token.replace('Bearer ', ''),
            'sidebarStatus': '1'
        }
        
        response = requests.get(
            url, 
            params=params, 
            headers=headers, 
            cookies=cookies, 
            verify=False
        )
        
        if response.status_code == 200:
            result = response.json()
            return result
        else:
            print(f"  获取创意列表失败，状态码: {response.status_code}")
            return None
    except Exception as e:
        print(f"  获取创意列表时发生错误: {str(e)}")
        return None

def copy_creative(file_ids, creative_id, account_id, ad_group_id, token, is_old=True, game_info=None):
    """复制创意"""
    try:
        url = 'http://game.raisedsun.com/prod-api/ad/account/copyCreative'
        
        headers = {
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
            'Authorization': token,
            'Content-Type': 'application/json;charset=UTF-8',
            'Origin': 'http://game.raisedsun.com',
            'Referer': f'http://game.raisedsun.com/cy/uid/{account_id}',
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********'
        }
        
        cookies = {
            'Admin-Token': token.replace('Bearer ', ''),
            'sidebarStatus': '1'
        }
        
        # 确保file_ids是列表类型
        if not isinstance(file_ids, list):
            file_ids = [file_ids]
        
        # 构建fileList - 从game_info中获取完整的文件信息
        file_list = []
        if game_info and 'rows' in game_info:
            rows = game_info['rows']
            for row in rows:
                if row.get('fileId') in file_ids:
                    file_list.append(row)
        
        data = {
            "fileIds": [],  # 改为空数组
            "creativeId": creative_id,
            "accountId": account_id,
            "currentAdGroupId": ad_group_id,
            "isGroup": False,
            "isOld": is_old,
            "fileList": file_list  # 新增fileList字段
        }
        
        response = requests.post(
            url, 
            headers=headers, 
            cookies=cookies, 
            json=data, 
            verify=False
        )
        
        if response.status_code == 200:
            result = response.json()
            return result
        else:
            print(f"  复制创意失败，状态码: {response.status_code}")
            return None
    except Exception as e:
        print(f"  复制创意时发生错误: {str(e)}")
        return None

def process_copy_creative_with_game_info(file_ids, uid, token, all_game_info):
    """处理复制创意的流程，使用完整的game_info，isOld始终为False"""
    if not file_ids:
        print("没有可用的fileId，无法进行复制创意操作")
        return
        
    # 获取创意列表
    print(f"\n获取UID {uid} 的创意列表...")
    creative_list = get_creative_list(uid, token)
    
    if not creative_list or 'rows' not in creative_list or not creative_list['rows']:
        print(f"未找到UID {uid} 的创意列表数据")
        return
    
    # 获取第一条数据的adgroupId和dynamicCreativeId
    first_row = creative_list['rows'][0]
    ad_group_id = first_row.get('adgroupId')
    dynamic_creative_id = first_row.get('dynamicCreativeId')
    
    if not ad_group_id or not dynamic_creative_id:
        print(f"未在创意列表中找到必要的ID信息")
        print(f"adgroupId: {ad_group_id}, dynamicCreativeId: {dynamic_creative_id}")
        return
    
    # 合并所有game_info的rows数据
    all_rows = []
    for game_info in all_game_info:
        if game_info and 'rows' in game_info:
            all_rows.extend(game_info['rows'])
    
    # 创建一个包含所有文件信息的game_info对象
    combined_game_info = {'rows': all_rows}
    
    # 直接用isOld=False一次性提交全部fileIds
    print(f"提交全部素材，isOld=False")
    result = copy_creative(file_ids, dynamic_creative_id, uid, ad_group_id, token, is_old=False, game_info=combined_game_info)
    if result and result.get('code') == 200:
        if len(result.get('data', [])) == 0:
            print("✅ 全部素材处理成功")
        else:
            print(f"⚠️  部分素材处理失败: {json.dumps(result.get('data', []), ensure_ascii=False)}")
    else:
        print(f"❌ 提交失败，状态码: {result.get('code') if result else '未知'}")

def main():
    # 加载配置
    config = load_config()
    api_url = config["api_url"]
    
    print("\n" + "🔍 " + "="*70)
    print("🚀 复制创意自动化工具启动")
    print("🔍 " + "="*70)
    
    # 获取所有配置
    print("\n📋 正在获取筛选配置...")
    result = get_all_filter_configs(api_url)
    
    # 检查API响应
    if result.get("code") != 20000 or not result.get("data"):
        print("❌ 获取配置失败:")
        print(f"   错误代码: {result.get('code')}")
        print(f"   错误信息: {result.get('message')}")
        sys.exit(1)
    
    # 提取并打印配置值
    configs = [c for c in result.get("data", []) if c.get("enabled")]
    print(f"✅ 成功获取 {len(configs)} 个配置组")
    print_config_values(configs)

    # 动态获取Token
    print("\n🔑 正在获取认证Token...")
    token = get_auth_token()
    if not token:
        print("❌ 获取认证Token失败，程序退出")
        sys.exit(1)
    print("✅ 认证Token获取成功")
    
    # 对每个配置单独处理
    for config_idx, config in enumerate(configs, 1):
        print(f"\n\n{'🔶'*30}")
        print(f"🔶 配置组 [{config_idx}/{len(configs)}]: {config.get('group_name', '未命名配置')}")
        print(f"{'🔶'*30}")
        
        app_id = config.get('appId')
        if not app_id:
            print(f"⚠️  配置组 #{config_idx} 没有设置应用ID，跳过处理")
            continue

        # 获取标签信息
        label_names = config.get('labelName', [])
        if not label_names:
            print(f"⚠️  配置组 #{config_idx} 没有设置标签，使用所有可用素材")
            label_names = [""]  # 使用空标签来获取所有素材

        # 处理该配置中的所有uid
        uids = config.get('uids', [])
        if not uids:
            print(f"⚠️  配置组 #{config_idx} 没有设置UID，跳过处理")
            continue

        print(f"\n📱 开始处理配置组 #{config_idx} 的 {len(uids)} 个UID")
        print(f"🏷️  使用标签: {label_names}")

        # 为每个标签获取素材 - 均匀分配数量
        all_file_ids = []
        all_game_info = []  # 保存所有game_info
        total_limit = 10  # 总共最多获取10个fileId
        
        # 计算每个标签应该获取的数量
        labels_count = len(label_names)
        per_label_limit = total_limit // labels_count if labels_count > 0 else total_limit
        remaining = total_limit % labels_count if labels_count > 0 else 0
        
        print(f"📊 标签分配策略: 总共{total_limit}个素材，{labels_count}个标签，每个标签{per_label_limit}个，剩余{remaining}个")
        
        for idx, label_name in enumerate(label_names):
            # 前几个标签多分配1个（处理余数）
            current_limit = per_label_limit + (1 if idx < remaining else 0)
            print(f"🏷️  查询标签: {label_name} (目标获取{current_limit}个)")
            
            game_info = get_game_info_by_label(label_name, token)
            
            if not game_info:
                print(f"❌ 获取素材失败")
                continue

            # 提取随机fileId，使用计算出的限制数量
            label_file_ids = extract_random_file_ids(game_info, limit=current_limit)
            if label_file_ids:
                all_file_ids.extend(label_file_ids)
                all_game_info.append(game_info)  # 保存game_info
                print(f"✅ 标签 '{label_name}' 实际获取到 {len(label_file_ids)} 个素材")
            else:
                print(f"⚠️  未找到可用素材")

        # 去重（保持原有逻辑以防万一有重复）
        all_file_ids = list(set(all_file_ids))  # 去重
        print(f"📋 去重后共获得 {len(all_file_ids)} 个唯一素材")

        if not all_file_ids:
            print(f"⚠️  配置组 #{config_idx} 没有找到任何可用的fileId，跳过处理")
            continue

        print(f"\n📋 配置组 #{config_idx} 总共获取到 {len(all_file_ids)} 个fileId")

        # 为每个UID处理创意复制
        for uid_idx, uid in enumerate(uids, 1):
            print(f"\n{'📱'*20}")
            print(f"📱 处理UID [{uid_idx}/{len(uids)}]: {uid}")
            print(f"{'📱'*20}")
                
            # 打印该UID将使用的fileId
            file_ids_str = " | ".join(str(fid) for fid in all_file_ids[:10])
            print(f"📄 UID {uid} 将使用的fileId: [{file_ids_str}]")

            # 使用新的处理函数，传入game_info
            process_copy_creative_with_game_info(all_file_ids, uid, token, all_game_info)
            
            print(f"\n✅ UID {uid} 处理完成")
        
        print(f"\n{'🏁'*20}")
        print(f"🏁 配置组 #{config_idx} 处理完成")
        print(f"{'🏁'*20}")
    
    print("\n" + "🎉 " + "="*70)
    print("🎉 所有配置处理完成！")
    print("🎉 " + "="*70)

if __name__ == "__main__":
    main()