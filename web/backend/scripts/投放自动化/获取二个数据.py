#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
运行数据获取服务的脚本
用于从外部API获取投放数据和小游戏数据

使用方法:
1. 获取数据:
   python 获取三个数据.py --days 7

2. 查看帮助信息:
   python 获取三个数据.py --help
"""

import os
import sys
import logging
import asyncio
from datetime import datetime

# 添加项目根目录到系统路径，以便正确导入模块
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.abspath(os.path.join(current_dir, '..', '..'))
sys.path.insert(0, project_root)

from src.services.data_fetcher import data_fetcher

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(f"data_fetcher_{datetime.now().strftime('%Y%m%d')}.log")
    ]
)

logger = logging.getLogger(__name__)

async def run_data_fetcher(days_ago=7):
    """运行数据获取服务（不再获取素材库目录）"""
    try:
        logger.info(f"开始获取数据，获取过去{days_ago}天的数据")

        # 获取授权
        await data_fetcher.get_authorization()

        # 获取投放账户数据
        accounts_result = await data_fetcher.fetch_ad_accounts(days_ago=days_ago)
        logger.info(f"投放账户数据获取完成: {accounts_result}")

        # 获取小游戏数据
        games_result = await data_fetcher.fetch_game_data(days_ago=days_ago)
        logger.info(f"小游戏数据获取完成: {games_result}")

        return {
            "accounts": accounts_result,
            "games": games_result
        }
    except Exception as e:
        logger.error(f"数据获取失败: {e}")
        raise

def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='运行数据获取服务')
    parser.add_argument('--days', type=int, default=7, help='获取过去几天的数据，默认7天')
    args = parser.parse_args()

    try:
        result = asyncio.run(run_data_fetcher(days_ago=args.days))
        logger.info(f"数据获取服务运行完成: {result}")
    except KeyboardInterrupt:
        logger.info("用户中断操作")
    except Exception as e:
        logger.error(f"运行出错: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main() 