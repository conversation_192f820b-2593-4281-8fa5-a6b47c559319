#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置查看器 - 通过API请求获取所有筛选配置并提取字段值
"""

import requests
import yaml
import os
import json
import sys
import datetime
from typing import Dict, Any, List
import urllib3
from creative_db import check_and_update_schema
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 配置文件路径
CONFIG_FILE = os.path.join(os.path.dirname(os.path.abspath(__file__)), "config.yaml")

def load_config() -> Dict[str, str]:
    """加载配置文件"""
    default_config = {"api_url": ""}
    
    if os.path.exists(CONFIG_FILE):
        try:
            with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f) or default_config
                if not config.get("api_url"):
                    print(f"错误: 配置文件中未设置api_url: {CONFIG_FILE}")
                    print("请编辑配置文件设置正确的API URL")
                    sys.exit(1)
                return config
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            sys.exit(1)
    else:
        # 创建默认配置文件
        try:
            with open(CONFIG_FILE, 'w', encoding='utf-8') as f:
                yaml.dump(default_config, f, default_flow_style=False, allow_unicode=True)
            print(f"已创建默认配置文件: {CONFIG_FILE}")
            print("请编辑配置文件设置正确的API URL后重新运行")
            sys.exit(1)
        except Exception as e:
            print(f"创建配置文件失败: {e}")
            sys.exit(1)
    
    return default_config

def get_all_filter_configs(api_url: str):
    """通过API请求获取所有筛选配置列表"""
    try:
        # 使用/filter-configs/all端点直接获取所有配置
        url = f"{api_url}/api/vue-element-admin/filter-configs/all"
        
        # 发送GET请求
        response = requests.get(url)
        
        # 返回JSON响应
        return response.json()
    except Exception as e:
        return {
            "code": 500,
            "message": f"获取筛选配置失败: {str(e)}",
            "data": None
        }

def print_config_values(configs: List[Dict[str, Any]]):
    """提取并打印配置中的各个字段值"""
    if not configs:
        print("没有找到任何配置")
        return
    
    print(f"\n找到 {len(configs)} 条配置记录:")
    print("=" * 80)
    
    # 打印各个配置
    for idx, config in enumerate(configs, 1):
        print(f"配置 #{idx}:")
        print(f"  ID: {config.get('id')}")
        print(f"  名称: {config.get('group_name')}")
        
        # 处理列表类型的字段
        filter_versions = config.get('filter_version', [])
        if filter_versions:
            print(f"  版本列表: {', '.join(str(v) for v in filter_versions)}")
        else:
            print("  版本列表: 空")
            
        filter_names = config.get('filter_name', [])
        if filter_names:
            print(f"  名称列表: {', '.join(str(n) for n in filter_names)}")
        else:
            print("  名称列表: 空")
            
        uids = config.get('uids', [])
        if uids:
            print(f"  UID列表: {', '.join(str(uid) for uid in uids)}")
        else:
            print("  UID列表: 空")

        # 显示group_name
        group_name = config.get('group_name', '')
        print(f"  配置组名称: {group_name}")

        # 处理数值类型的字段
        print(f"  最小花费: {config.get('min_cost', 0)}")
        print(f"  最小ROI: {config.get('min_MonetizationRoi', 0)}")

        # 处理其他字段
        print(f"  应用ID: {config.get('appId', '')}")
        print(f"  状态: {'启用' if config.get('enabled') else '禁用'}")
        print(f"  创建时间: {config.get('created_at', '')}")
        print(f"  更新时间: {config.get('updated_at', '')}")
        print("-" * 80)

def get_auth_token():
    """获取Authorization值"""
    try:
        # 获取authorization
        auth_headers = {
            'Authorization': 'your_secret_token_123'
        }
        auth_response = requests.get('http://***************:5000/get/tfpt', headers=auth_headers)
        authorization = auth_response.text

        if auth_response.status_code == 200:
            # 确保返回的token包含Bearer前缀
            token = authorization.strip()
            if not token.startswith('Bearer '):
                token = f'Bearer {token}'
            return token
        else:
            print(f"获取Authorization失败，状态码: {auth_response.status_code}")
            return None
    except Exception as e:
        print(f"获取Authorization时发生错误: {str(e)}")
        return None

def get_fileids_from_database(group_name: str, uid: int, limit=10):
    """从数据库根据group_name和uid获取该UID未使用过的fileId和fileId_data"""
    try:
        # 使用新的函数获取该UID未使用过的fileId
        from creative_db import get_unused_fileids_for_uid
        file_data_list = get_unused_fileids_for_uid(group_name, uid, limit)
        if file_data_list:
            file_ids = [str(data[0]) for data in file_data_list]  # 提取fileId
            print(f"  ✅ 从数据库获取到 {len(file_ids)} 个UID {uid} 未使用的fileId")
            return file_ids, file_data_list
        else:
            print(f"  ⚠️  数据库中没有找到group_name='{group_name}'中UID {uid} 未使用的fileId")
            return [], []
    except ImportError:
        # 如果新函数不存在，回退到原有逻辑
        try:
            from creative_db import get_all_fileids_by_group
            file_data_list = get_all_fileids_by_group(group_name, limit)
            if file_data_list:
                file_ids = [str(data[0]) for data in file_data_list]  # 提取fileId
                print(f"  ✅ 从数据库获取到 {len(file_ids)} 个fileId（回退模式，无使用状态检查）")
                return file_ids, file_data_list
            else:
                print(f"  ⚠️  数据库中没有找到group_name='{group_name}'的fileId")
                return [], []
        except Exception as e:
            print(f"  ❌ 数据库查询出错 - 错误信息: {str(e)}")
            return [], []
    except Exception as e:
        print(f"  ❌ 数据库查询出错 - 错误信息: {str(e)}")
        return [], []

def get_creative_list(uid, token):
    """获取创意列表，返回最近7天的数据"""
    try:
        # 计算最近7天的日期范围
        end_date = datetime.datetime.now()
        start_date = end_date - datetime.timedelta(days=7)
        
        # 格式化日期为YYYY-MM-DD
        start_date_str = start_date.strftime('%Y-%m-%d')
        end_date_str = end_date.strftime('%Y-%m-%d')
        
        url = 'http://game.raisedsun.com/prod-api/ad/account/creativeList'
        params = {
            'uid': uid,
            'pageNum': 1,
            'pageSize': 20,      # 限制为20条，因为我们只需要第一条数据
            'startDate': start_date_str,
            'endDate': end_date_str,
            'operationStatus': 3,
            'sortField': 'cost',
            'sortOrder': 'DESCENDING',
            'isEmpty': 'false'
        }
        
        headers = {
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
            'Authorization': token,
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0',
            'Referer': f'http://game.raisedsun.com/cy/uid/{uid}'
        }
        
        cookies = {
            'Admin-Token': token.replace('Bearer ', ''),
            'sidebarStatus': '1'
        }
        
        response = requests.get(
            url, 
            params=params, 
            headers=headers, 
            cookies=cookies, 
            verify=False
        )
        
        if response.status_code == 200:
            result = response.json()
            return result
        else:
            print(f"  获取创意列表失败，状态码: {response.status_code}")
            return None
    except Exception as e:
        print(f"  获取创意列表时发生错误: {str(e)}")
        return None

def copy_creative(file_ids, creative_id, account_id, ad_group_id, token, is_old=True, file_data_list=None):
    """复制创意"""
    try:
        url = 'http://game.raisedsun.com/prod-api/ad/account/copyCreative'
        
        headers = {
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
            'Authorization': token,
            'Content-Type': 'application/json;charset=UTF-8',
            'Origin': 'http://game.raisedsun.com',
            'Referer': f'http://game.raisedsun.com/cy/uid/{account_id}',
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0'
        }
        
        cookies = {
            'Admin-Token': token.replace('Bearer ', ''),
            'sidebarStatus': '1'
        }
        
        # 确保file_ids是列表类型
        if not isinstance(file_ids, list):
            file_ids = [file_ids]
        
        # 构建fileList - 从数据库的fileId_data中解析
        file_list = []
        if file_data_list:
            for file_id, file_id_data in file_data_list:
                if str(file_id) in [str(fid) for fid in file_ids]:
                    try:
                        # 解析fileId_data JSON字符串
                        file_info = json.loads(file_id_data) if isinstance(file_id_data, str) else file_id_data
                        file_list.append(file_info)
                    except (json.JSONDecodeError, TypeError) as e:
                        print(f"  ⚠️  解析fileId_data失败: {e}")
                        continue
        
        data = {
            "fileIds": [],  # 改为空数组
            "creativeId": creative_id,
            "accountId": account_id,
            "currentAdGroupId": ad_group_id,
            "isGroup": False,
            "isOld": is_old,
            "fileList": file_list  # 新增fileList字段
        }
        
        response = requests.post(
            url, 
            headers=headers, 
            cookies=cookies, 
            json=data, 
            verify=False
        )
        
        if response.status_code == 200:
            result = response.json()
            return result
        else:
            print(f"  复制创意失败，状态码: {response.status_code}")
            return None
    except Exception as e:
        print(f"  复制创意时发生错误: {str(e)}")
        return None

def process_copy_creative_with_database(file_ids, uid, token, file_data_list, group_name):
    """处理复制创意的流程，使用数据库获取的fileId，isOld始终为False"""
    if not file_ids:
        print("没有可用的fileId，无法进行复制创意操作")
        return
        
    # 获取创意列表
    print(f"\n获取UID {uid} 的创意列表...")
    creative_list = get_creative_list(uid, token)
    
    if not creative_list or 'rows' not in creative_list or not creative_list['rows']:
        print(f"未找到UID {uid} 的创意列表数据")
        return
    
    # 获取第一条数据的adgroupId和dynamicCreativeId
    first_row = creative_list['rows'][0]
    ad_group_id = first_row.get('adgroupId')
    dynamic_creative_id = first_row.get('dynamicCreativeId')
    
    if not ad_group_id or not dynamic_creative_id:
        print(f"未在创意列表中找到必要的ID信息")
        print(f"adgroupId: {ad_group_id}, dynamicCreativeId: {dynamic_creative_id}")
        return
    
    # 直接用isOld=False一次性提交全部fileIds
    print(f"提交全部素材，isOld=False")
    result = copy_creative(file_ids, dynamic_creative_id, uid, ad_group_id, token, is_old=False, file_data_list=file_data_list)
    
    # 无论API调用结果如何，都标记这些fileId为该UID已使用
    # 这样可以避免重复使用，即使出现重复创意等错误也要记录使用状态
    try:
        from creative_db import mark_fileids_used_for_uid
        success_count = mark_fileids_used_for_uid(file_ids, uid, group_name)
        print(f"  ✅ 已标记 {success_count}/{len(file_ids)} 个fileId为UID {uid} 已使用")
    except ImportError:
        print(f"  ⚠️  无法标记fileId使用状态（函数不存在）")
    except Exception as e:
        print(f"  ⚠️  标记fileId使用状态失败: {e}")
    
    # 处理API响应结果
    if result and result.get('code') == 200:
        if len(result.get('data', [])) == 0:
            print("✅ 全部素材处理成功")
        else:
            print(f"⚠️  部分素材处理失败: {json.dumps(result.get('data', []), ensure_ascii=False)}")
    else:
        print(f"❌ 提交失败，状态码: {result.get('code') if result else '未知'}")

def main():
    # 首先检查并更新数据库架构
    print("\n" + "🔍 " + "="*70)
    print("🚀 复制创意自动化工具启动")
    print("🔍 " + "="*70)
    
    print("\n🔧 正在检查数据库架构...")
    try:
        schema_updated = check_and_update_schema()
        if schema_updated:
            print("✅ 数据库架构已更新到最新版本")
        else:
            print("✅ 数据库架构检查完成")
    except Exception as e:
        print(f"❌ 数据库架构检查失败: {e}")
        return
    
    # 加载配置
    config = load_config()
    api_url = config["api_url"]
    
    # 获取所有配置
    print("\n📋 正在获取筛选配置...")
    result = get_all_filter_configs(api_url)
    
    # 检查API响应
    if result.get("code") != 20000 or not result.get("data"):
        print("❌ 获取配置失败:")
        print(f"   错误代码: {result.get('code')}")
        print(f"   错误信息: {result.get('message')}")
        sys.exit(1)
    
    # 提取并打印配置值
    configs = [c for c in result.get("data", []) if c.get("enabled")]
    print(f"✅ 成功获取 {len(configs)} 个配置组")
    print_config_values(configs)

    # 动态获取Token
    print("\n🔑 正在获取认证Token...")
    token = get_auth_token()
    if not token:
        print("❌ 获取认证Token失败，程序退出")
        sys.exit(1)
    print("✅ 认证Token获取成功")
    
    # 对每个配置单独处理
    for config_idx, config in enumerate(configs, 1):
        print(f"\n\n{'🔶'*30}")
        print(f"🔶 配置组 [{config_idx}/{len(configs)}]: {config.get('group_name', '未命名配置')}")
        print(f"{'🔶'*30}")
        
        app_id = config.get('appId')
        if not app_id:
            print(f"⚠️  配置组 #{config_idx} 没有设置应用ID，跳过处理")
            continue

        # 获取group_name
        group_name = config.get('group_name')
        if not group_name:
            print(f"⚠️  配置组 #{config_idx} 没有设置group_name，跳过处理")
            continue

        # 处理该配置中的所有uid
        uids = config.get('uids', [])
        if not uids:
            print(f"⚠️  配置组 #{config_idx} 没有设置UID，跳过处理")
            continue

        print(f"\n📱 开始处理配置组 #{config_idx} 的 {len(uids)} 个UID")
        print(f"🗂️  使用数据库group_name: {group_name}")

        # 为每个UID处理创意复制
        for uid_idx, uid in enumerate(uids, 1):
            print(f"\n{'📱'*20}")
            print(f"📱 处理UID [{uid_idx}/{len(uids)}]: {uid}")
            print(f"{'📱'*20}")
            
            # 为每个UID单独获取该UID未使用过的fileId
            per_uid_limit = 10  # 每个UID最多获取10个fileId
            print(f"📊 为UID {uid} 从数据库获取策略: 最多获取{per_uid_limit}个该UID未使用的fileId")
            
            uid_file_ids, uid_file_data_list = get_fileids_from_database(group_name, uid, limit=per_uid_limit)
            
            if not uid_file_ids:
                print(f"⚠️  UID {uid} 在数据库中没有找到任何可用的fileId，跳过处理")
                continue
                
            # 打印该UID将使用的fileId
            file_ids_str = " | ".join(str(fid) for fid in uid_file_ids)
            print(f"📄 UID {uid} 获取到 {len(uid_file_ids)} 个未使用的fileId: [{file_ids_str}]")

            # 使用新的处理函数，传入数据库获取的数据
            process_copy_creative_with_database(uid_file_ids, uid, token, uid_file_data_list, group_name)
            
            print(f"\n✅ UID {uid} 处理完成")
        
        print(f"\n{'🏁'*20}")
        print(f"🏁 配置组 #{config_idx} 处理完成")
        print(f"{'🏁'*20}")
    
    print("\n" + "🎉 " + "="*70)
    print("🎉 所有配置处理完成！")
    print("🎉 " + "="*70)

if __name__ == "__main__":
    print("脚本开始执行...")
    try:
        main()
    except Exception as e:
        print(f"脚本执行出错: {e}")
        import traceback
        traceback.print_exc()