import requests
import json
import sys
import logging
from datetime import datetime
from creative_db import init_db, update_creative_fileid, get_creatives_by_url

class Config:
    """配置管理类"""
    # API配置
    TOKEN_SERVICE_URL = 'http://***************:5000/get/tfpt'
    DEFAULT_API_URL = "http://game.raisedsun.com/prod-api/system/info/list?pageNum=1&pageSize=500&fileType=1&groupId=416&isGroupType=true"
    
    # 请求配置
    REQUEST_TIMEOUT = 30
    MAX_RETRIES = 3
    RETRY_DELAY = 2
    
    # 日志配置
    LOG_LEVEL = logging.INFO
    
    # 批处理配置
    BATCH_SIZE = 100  # 可用于未来的分批处理优化

# 配置日志（仅控制台输出）
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)

def get_authorization(max_retries=3):
    """动态获取Authorization token，支持重试机制"""
    for attempt in range(max_retries):
        try:
            logging.info(f"尝试获取Authorization token (第{attempt + 1}次)")
            response = requests.get('http://***************:5000/get/tfpt', timeout=10)
            if response.status_code == 200:
                token = response.text.strip()
                logging.info("成功获取Authorization token")
                return token
            else:
                raise Exception(f"获取Authorization失败，状态码: {response.status_code}")
        except Exception as e:
            logging.warning(f"第{attempt + 1}次获取Authorization失败: {e}")
            if attempt == max_retries - 1:
                logging.error("所有重试均失败，程序无法继续执行")
                print(f"获取Authorization失败: {e}")
                print("程序无法继续执行，请检查token服务是否正常")
                sys.exit(1)
            else:
                import time
                time.sleep(2)  # 等待2秒后重试

def fetch_file_data(api_url, token):
    """请求API获取fileId数据"""
    headers = {
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
        'Authorization': token,
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0',
        'Proxy-Connection': 'keep-alive',
        'Referer': 'http://game.raisedsun.com/ad/material'
    }
    
    # 设置cookies
    cookies = {
        'Admin-Token': token.replace('Bearer ', ''),
        'sidebarStatus': '1'
    }
    
    try:
        response = requests.get(api_url, headers=headers, cookies=cookies, verify=False)
        if response.status_code == 200:
            data = response.json()
            return data
        elif response.status_code == 401:
            print(f"认证失败(401): token可能已过期，请检查Authorization")
            return None
        else:
            print(f"API请求失败，状态码: {response.status_code}")
            print(f"响应内容: {response.text[:200]}...")
            return None
    except requests.exceptions.RequestException as e:
        print(f"网络请求失败: {e}")
        return None
    except json.JSONDecodeError as e:
        print(f"JSON解析失败: {e}")
        print(f"响应内容: {response.text[:200]}...")
        return None
    except Exception as e:
        print(f"请求API失败: {e}")
        return None

def process_file_data(data):
    """处理API响应数据，更新数据库"""
    if not data or 'rows' not in data:
        logging.error("API响应数据格式错误")
        print("API响应数据格式错误")
        return
    
    total_processed = 0
    updated_count = 0
    not_found_count = 0
    error_count = 0
    
    start_time = datetime.now()
    logging.info(f"开始处理 {len(data['rows'])} 条fileId数据")
    print(f"开始处理 {len(data['rows'])} 条fileId数据...")
    
    # 批处理优化：预先获取所有URL对应的创意记录
    url_to_creatives = {}
    for row in data['rows']:
        url = row.get('url', '').strip()
        if url and url not in url_to_creatives:
            url_to_creatives[url] = get_creatives_by_url(url)
    
    for row in data['rows']:
        total_processed += 1
        file_id = row.get('fileId')
        url = row.get('url', '').strip()
        flag = row.get('flag')  # 获取flag值

        if not url:
            logging.warning(f"第{total_processed}条数据URL为空，跳过")
            continue

        try:
            # 使用预先获取的创意记录
            creative_records = url_to_creatives.get(url, [])

            if creative_records:
                # 将整个row数据转换为JSON字符串保存
                file_id_data = json.dumps(row, ensure_ascii=False)

                # 更新所有匹配的记录
                for creative_id, _ in creative_records:
                    update_creative_fileid(creative_id, file_id, file_id_data, flag)
                    updated_count += 1
                    logging.info(f"更新创意ID {creative_id}: fileId={file_id}, flag={flag}")

                print(f"✓ 更新创意ID {[r[0] for r in creative_records]}: fileId={file_id}, flag={flag}, url={url[:50]}...")
            else:
                not_found_count += 1
                logging.warning(f"未找到URL对应的创意记录: {url}")

        except Exception as e:
            error_count += 1
            logging.error(f"处理第{total_processed}条数据时出错: {e}")
            print(f"✗ 处理第{total_processed}条数据时出错: {e}")
    
    end_time = datetime.now()
    duration = (end_time - start_time).total_seconds()
    
    # 输出处理结果
    result_summary = f"""
处理完成 (耗时: {duration:.2f}秒):
总处理数据: {total_processed}
成功更新: {updated_count}
未找到匹配: {not_found_count}
处理错误: {error_count}
处理效率: {total_processed/duration:.2f} 条/秒
"""
    
    # 使用logging输出到控制台
    logging.info(result_summary)

def main():
    """主函数"""
    # 初始化数据库
    init_db()
    
    # 获取Authorization token
    print("获取Authorization token...")
    token = get_authorization(Config.MAX_RETRIES)
    print("✓ 成功获取token")
    
    # 直接使用默认API URL
    api_url = Config.DEFAULT_API_URL
    print(f"\n使用API URL: {api_url}")
    
    logging.info(f"开始获取fileId数据，API URL: {api_url}")
    data = fetch_file_data(api_url, token)
    
    if data:
        print(f"✓ 成功获取API数据，总计 {data.get('total', 0)} 条记录")
        process_file_data(data)
    else:
        print("✗ 获取API数据失败")
        print("\n可能的解决方案:")
        print("1. 检查网络连接")
        print("2. 确认token是否有效")
        print("3. 验证API URL是否正确")
        print("4. 检查API服务是否正常运行")
        sys.exit(1)

if __name__ == "__main__":
    main()