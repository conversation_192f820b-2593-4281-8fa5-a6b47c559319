import requests
import yaml
import os
import sys
from typing import Dict
from creative_db import init_db, insert_creative
import sqlite3
from datetime import datetime, timedelta
import time
import threading
import random
from concurrent.futures import ThreadPoolExecutor, as_completed

def requests_with_retry(method, url, max_retries=3, **kwargs):
    """带重试机制的网络请求函数"""
    for attempt in range(max_retries):
        try:
            if method.upper() == 'GET':
                response = requests.get(url, **kwargs)
            elif method.upper() == 'POST':
                response = requests.post(url, **kwargs)
            else:
                raise ValueError(f"不支持的HTTP方法: {method}")
            
            if response.status_code == 200:
                return response
            else:
                if attempt < max_retries - 1:
                    wait_time = (2 ** attempt) + random.uniform(0, 1)
                    print(f"[线程{threading.current_thread().name}] 请求失败(状态码:{response.status_code})，{wait_time:.1f}秒后重试...")
                    time.sleep(wait_time)
                else:
                    print(f"[线程{threading.current_thread().name}] 请求失败，已达到最大重试次数")
                    return response
        except Exception as e:
            if attempt < max_retries - 1:
                wait_time = (2 ** attempt) + random.uniform(0, 1)
                print(f"[线程{threading.current_thread().name}] 请求异常: {e}，{wait_time:.1f}秒后重试...")
                time.sleep(wait_time)
            else:
                print(f"[线程{threading.current_thread().name}] 请求异常，已达到最大重试次数: {e}")
                raise e
    return None

def get_authorization():
    """动态获取Authorization token"""
    try:
        response = requests_with_retry('GET', 'http://***************:5000/get/tfpt')
        if response and response.status_code == 200:
            return response.text.strip()
        else:
            raise Exception(f"获取Authorization失败，状态码: {response.status_code if response else 'None'}")
    except Exception as e:
        print(f"获取Authorization失败: {e}")
        print("程序无法继续执行，请检查token服务是否正常")
        sys.exit(1)

# 获取authorization token
token = get_authorization()


# 配置文件路径
CONFIG_FILE = os.path.join(os.path.dirname(os.path.abspath(__file__)), "config.yaml")

def load_config() -> Dict[str, str]:
    """加载配置文件"""
    default_config = {"api_url": ""}
    if os.path.exists(CONFIG_FILE):
        try:
            with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f) or default_config
                if not config.get("api_url"):
                    print(f"错误: 配置文件中未设置api_url: {CONFIG_FILE}")
                    print("请编辑配置文件设置正确的API URL")
                    sys.exit(1)
                return config
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            sys.exit(1)
    else:
        # 创建默认配置文件
        try:
            with open(CONFIG_FILE, 'w', encoding='utf-8') as f:
                yaml.dump(default_config, f, default_flow_style=False, allow_unicode=True)
            print(f"已创建默认配置文件: {CONFIG_FILE}")
            print("请编辑配置文件设置正确的API URL后重新运行")
            sys.exit(1)
        except Exception as e:
            print(f"创建配置文件失败: {e}")
            sys.exit(1)
    return default_config

def get_all_filter_configs(api_url: str):
    """通过API请求获取所有筛选配置列表"""
    try:
        url = f"{api_url}/api/vue-element-admin/filter-configs/all"
        response = requests_with_retry('GET', url)
        if response:
            return response.json()
        else:
            return {
                "code": 500,
                "message": "获取筛选配置失败: 请求返回空响应",
                "data": None
            }
    except Exception as e:
        return {
            "code": 500,
            "message": f"获取筛选配置失败: {str(e)}",
            "data": None
        }

def fetch_uids(api_url, token, version_search=None, name_search=None, min_cost=None):
    """
    获取账户uid列表，可根据版本或名称筛选
    """
    url = f"{api_url}/api/vue-element-admin/accounts"
    params = {
        "page": 1,
        "limit": 5000
    }
    if min_cost is not None:
        params["min_cost"] = min_cost
    if version_search:
        params["version_search"] = version_search
    if name_search:
        params["name_search"] = name_search

    headers = {
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
        'Authorization': token,
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
        'Referer': 'http://localhost:9527/',
        'Origin': 'http://localhost:9527',
        'Connection': 'keep-alive'
    }
    response = requests_with_retry('GET', url, headers=headers, params=params)
    if response and response.status_code == 200:
        data = response.json()
        if data.get("code") == 20000:
            return [item["uid"] for item in data["data"]["items"]]
        else:
            print("API返回错误:", data.get("message"))
    else:
        print("请求失败，状态码:", response.status_code if response else "None")
    return []

def get_creative_count():
    conn = sqlite3.connect('creative_data.db')
    c = conn.cursor()
    c.execute('SELECT COUNT(*) FROM creative')
    count = c.fetchone()[0]
    conn.close()
    return count

# 修改insert_creative调用逻辑，统计新增和更新
added_count = 0
updated_count = 0
group_stats = {}  # 记录每个配置组的统计信息
db_lock = threading.Lock()  # 数据库操作锁

def fetch_and_save_creative_list_with_retry(gameurl, token, uid, filter_version, filter_name, config_uids, group_name, min_cost, min_roi, max_retries=3):
    """带重试机制的获取和保存创意列表函数"""
    for attempt in range(max_retries):
        try:
            result = fetch_and_save_creative_list(gameurl, token, uid, filter_version, filter_name, config_uids, group_name, min_cost, min_roi)
            return result
        except Exception as e:
            if attempt < max_retries - 1:
                wait_time = (2 ** attempt) + random.uniform(0, 1)
                print(f"[线程{threading.current_thread().name}] UID {uid} 处理异常: {e}，{wait_time:.1f}秒后重试...")
                time.sleep(wait_time)
            else:
                print(f"[线程{threading.current_thread().name}] UID {uid} 经过 {max_retries} 次重试后仍然失败: {e}")
                return None
    return None

def fetch_and_save_creative_list(gameurl, token, uid, filter_version, filter_name, config_uids, group_name, min_cost, min_roi):
    global added_count, updated_count
    end_date = datetime.now()
    start_date = end_date - timedelta(days=7)
    start_date_str = start_date.strftime('%Y-%m-%d')
    end_date_str = end_date.strftime('%Y-%m-%d')
    url = f"{gameurl}/prod-api/ad/account/creativeList"
    params = {
        "uid": uid,
        "pageNum": 1,
        "pageSize": 100,
        "startDate": start_date_str,
        "endDate": end_date_str,
        "operationStatus": 3,
        "sortField": "cost",
        "sortOrder": "DESCENDING",
        "isEmpty": "false"
    }
    headers = {
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
        'Authorization': token,
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
        'Referer': f'http://game.raisedsun.com/cy/uid/{uid}',
        'Proxy-Connection': 'keep-alive'
    }
    cookies = {
        'Admin-Token': token.replace('Bearer ', ''),
        'sidebarStatus': '1'
    }
    response = requests_with_retry('GET', url, headers=headers, cookies=cookies, params=params, verify=False)

    if response and response.status_code == 200:
        try:
            data = response.json()

            
            if data.get("rows"):
                uid_added_count = 0
                uid_updated_count = 0
                uid_filtered_count = 0
                
                for idx, row in enumerate(data["rows"], 1):
                    
                    # cost过滤
                    try:
                        row_cost = float(row.get("cost", 0))
                    except Exception as e:
                        row_cost = 0
                    try:
                        row_roi = float(row.get("miniGameAdMonetizationRoi", 0))
                    except Exception as e:
                        row_roi = 0
                    
                    if row_cost < min_cost or row_roi < min_roi:
                        uid_filtered_count += 1
                        continue
                    
                    # videoId过滤 - 跳过视频创意
                    video_id = row.get("videoId")
                    if video_id is not None and video_id != "" and str(video_id).strip() != "":
                        uid_filtered_count += 1
                        continue
                    
                    # 判断是新增还是更新（使用线程锁保证并发安全）
                    with db_lock:
                        conn = sqlite3.connect('creative_data.db')
                        c = conn.cursor()
                        dynamicCreativeId = row.get("dynamicCreativeId")
                        c.execute('SELECT id FROM creative WHERE group_name=? AND dynamicCreativeId=?', (group_name, dynamicCreativeId))
                        exists = c.fetchone()
                        conn.close()
                        
                        if exists:
                            updated_count += 1
                            uid_updated_count += 1
                        else:
                            added_count += 1
                            uid_added_count += 1
                        
                        try:
                            insert_creative(uid, row, filter_version=filter_version, filter_name=filter_name, config_uids=config_uids, group_name=group_name)
                        except Exception as e:
                            print(f"     [线程{threading.current_thread().name}] ❌ 数据库操作失败: {e}")
                            return False
                
                total_processed = uid_added_count + uid_updated_count
                print(f"[线程{threading.current_thread().name}] 已保存UID {uid} 的 {total_processed} 条创意数据（新增: {uid_added_count}, 更新: {uid_updated_count}, 过滤: {uid_filtered_count}）")
                return True
            else:
                print(f"[线程{threading.current_thread().name}] UID {uid} 没有创意数据或rows为空")
                return True  # 没有数据也算成功
        except Exception as e:
            print(f"[线程{threading.current_thread().name}] UID {uid} JSON解析失败: {e}")
            return False
    else:
        if response:
            print(f"[线程{threading.current_thread().name}] UID {uid} HTTP请求失败，状态码: {response.status_code}")
        else:
            print(f"[线程{threading.current_thread().name}] UID {uid} HTTP请求失败，无响应")
        return False

if __name__ == "__main__":
    config = load_config()
    api_url = config["api_url"]
    gameurl = config.get("gameurl", api_url)

    print("正在获取筛选配置...")
    result = get_all_filter_configs(api_url)
    if result.get("code") != 20000 or not result.get("data"):
        print("❌ 获取配置失败:")
        print(f"   错误代码: {result.get('code')}")
        print(f"   错误信息: {result.get('message')}")
        sys.exit(1)
    configs = result.get("data", [])
    print(f"✅ 成功获取 {len(configs)} 个配置组")
    # 只打印启用的配置组
    enabled_configs = [c for c in configs if c.get('enabled')]
    if not enabled_configs:
        print("没有找到任何启用的配置")
    else:
        print("\n找到 {} 条启用的配置记录:".format(len(enabled_configs)))
        print("=" * 80)
        init_db()
        for idx, config in enumerate(enabled_configs, 1):
            print(f"配置 #{idx}:")
            print(f"  ID: {config.get('id')}")
            print(f"  名称: {config.get('group_name')}")
            filter_versions = config.get('filter_version', [])
            print(f"  筛选版本: {', '.join(str(v) for v in filter_versions) if filter_versions else '空'}")
            filter_names = config.get('filter_name', [])
            print(f"  筛选名称: {', '.join(str(n) for n in filter_names) if filter_names else '空'}")
            min_cost = config.get('min_cost', 0)
            uids = []
            # 优先用筛选版本，其次筛选名称，否则不加筛选
            if filter_versions:
                for version in filter_versions:
                    uids += fetch_uids(api_url, token, version_search=version, min_cost=min_cost)
            elif filter_names:
                for name in filter_names:
                    uids += fetch_uids(api_url, token, name_search=name, min_cost=min_cost)
            else:
                uids = fetch_uids(api_url, token, min_cost=min_cost)
            uids = list(set(uids))  # 去重
            group_name = config.get('group_name', '')
            config_uids = config.get('uids', [])
            print(f"  原始配置组UID: {', '.join(str(u) for u in config_uids) if config_uids else '空'}")
            
            # 初始化该组的统计信息
            group_stats[group_name] = {
                'uids': [],
                'total_added': 0,
                'total_updated': 0,
                'total_filtered': 0,
                'uid_details': {}
            }
            
            if uids:
                print(f"  获取到 {len(uids)} 个UID: {', '.join(str(u) for u in uids[:10])}{'...' if len(uids) > 10 else ''}")
                group_stats[group_name]['uids'] = uids
                
                # 记录处理前的计数
                before_added = added_count
                before_updated = updated_count
                
                filter_version_str = ','.join(str(v) for v in filter_versions) if filter_versions else None
                filter_name_str = ','.join(str(n) for n in filter_names) if filter_names else None
                config_uids_str = ','.join(str(u) for u in config_uids) if config_uids else None
                min_roi = config.get('min_MonetizationRoi', 0)
                
                # 使用线程池进行并发处理
                max_workers = min(5, len(uids))  # 最大5个线程，或UID数量
                print(f"  使用 {max_workers} 个线程并发处理 {len(uids)} 个UID...")
                
                with ThreadPoolExecutor(max_workers=max_workers) as executor:
                    # 提交所有UID处理任务
                    future_to_uid = {}
                    for uid in uids:
                        future = executor.submit(
                            fetch_and_save_creative_list_with_retry,
                            gameurl, token, uid, filter_version_str, filter_name_str, 
                            config_uids_str, group_name, min_cost, min_roi
                        )
                        future_to_uid[future] = uid
                    
                    # 处理完成的任务
                    success_count = 0
                    failed_count = 0
                    for future in as_completed(future_to_uid):
                        uid = future_to_uid[future]
                        try:
                            result = future.result()
                            if result is True:
                                print(f"  ✓ UID {uid} 处理完成")
                                success_count += 1
                            else:
                                print(f"  ✗ UID {uid} 处理失败")
                                failed_count += 1
                        except Exception as e:
                            print(f"  ✗ UID {uid} 处理异常: {e}")
                            failed_count += 1
                    
                    print(f"  📊 UID处理统计: 成功 {success_count}, 失败 {failed_count}, 总计 {len(uids)}")
                
                # 计算该组的总增量
                group_added = added_count - before_added
                group_updated = updated_count - before_updated
                group_stats[group_name]['total_added'] = group_added
                group_stats[group_name]['total_updated'] = group_updated
                
                print(f"  配置组 {group_name} 处理完成: 新增 {group_added}, 更新 {group_updated}")
            else:
                print("  未获取到任何UID")
            label_names = config.get('labelName', [])
            if label_names:
                print(f"  标签列表: {', '.join(str(label) for label in label_names)}")
            else:
                print("  标签列表: 空")
            print(f"  最小花费: {config.get('min_cost', 0)}")
            print(f"  最小ROI: {config.get('min_MonetizationRoi', 0)}")
            print(f"  appid: {config.get('appId', '')}")
            print(f"  状态: {'启用' if config.get('enabled') else '禁用'}")
            print(f"  创建时间: {config.get('created_at', '')}")
            print(f"  更新时间: {config.get('updated_at', '')}") 
            print("-" * 80)
    
    # 输出详细的分组统计
    if group_stats:
        print("\n📊 详细分组统计:")
        print("=" * 60)
        for group_name, stats in group_stats.items():
            print(f"\n🔸 配置组: {group_name}")
            print(f"   处理的UID数量: {len(stats['uids'])}")
            print(f"   组内新增总数: {stats['total_added']}")
            print(f"   组内更新总数: {stats['total_updated']}")
            
            if stats['uid_details']:
                print(f"   各UID详细统计:")
                for uid, details in stats['uid_details'].items():
                    total_uid = details['added'] + details['updated']
                    print(f"     UID {uid}: 共{total_uid}条 (新增:{details['added']}, 更新:{details['updated']})")
            print("-" * 50)
        print("=" * 60)
    
    print("\n============================")
    print(f"本次新增: {added_count}")
    print(f"本次更新: {updated_count}")
    print(f"creative表总数: {get_creative_count()}")
    print("============================")