import requests
from datetime import datetime, timedelta
import yaml
import os
import sys
from typing import Dict

# 配置文件路径
CONFIG_FILE = os.path.join(os.path.dirname(os.path.abspath(__file__)), "config.yaml")

def load_config() -> Dict[str, str]:
    """加载配置文件"""
    default_config = {"api_url": ""}
    
    if os.path.exists(CONFIG_FILE):
        try:
            with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f) or default_config
                if not config.get("api_url"):
                    print(f"错误: 配置文件中未设置api_url: {CONFIG_FILE}")
                    print("请编辑配置文件设置正确的API URL")
                    sys.exit(1)
                return config
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            sys.exit(1)
    else:
        # 创建默认配置文件
        try:
            with open(CONFIG_FILE, 'w', encoding='utf-8') as f:
                yaml.dump(default_config, f, default_flow_style=False, allow_unicode=True)
            print(f"已创建默认配置文件: {CONFIG_FILE}")
            print("请编辑配置文件设置正确的API URL后重新运行")
            sys.exit(1)
        except Exception as e:
            print(f"创建配置文件失败: {e}")
            sys.exit(1)
    
    return default_config

def get_all_filter_configs(api_url: str):
    """通过API请求获取所有筛选配置列表"""
    try:
        # 使用/filter-configs/all端点直接获取所有配置
        url = f"{api_url}/api/vue-element-admin/filter-configs/all"
        
        # 发送GET请求
        response = requests.get(url)
        
        # 返回JSON响应
        return response.json()
    except Exception as e:
        return {
            "code": 500,
            "message": f"获取筛选配置失败: {str(e)}",
            "data": None
        }

# 获取authorization
auth_headers = {
    'Authorization': 'your_secret_token_123'
}
auth_response = requests.get('http://124.221.208.141:5000/get/tfpt', headers=auth_headers)
AUTHORIZATION = auth_response.text

# 计算最近7天的日期范围
end_date = datetime.today()
start_date = end_date - timedelta(days=7)

# 将日期格式化为'YYYY-MM-DD'字符串格式
start_date_str = start_date.strftime('%Y-%m-%d')
end_date_str = end_date.strftime('%Y-%m-%d')

def parse_creative_date(creative_name):
    """
    解析创意名称中的日期
    @param {string} creative_name - 创意名称，格式如 'copy_20250228173812921'
    @return {datetime|None} - 返回日期对象或None
    """
    try:
        if not creative_name or not isinstance(creative_name, str):
            return None
        # 提取日期部分 (YYYYMMDD)
        date_str = creative_name.split('_')[1][:8]
        return datetime.strptime(date_str, '%Y%m%d')
    except (IndexError, ValueError):
        return None

def should_protect_creative(creative_name, cost, days=5, cost_threshold=10):
    """
    判断创意是否需要保护（基于日期和花费）
    @param {string} creative_name - 创意名称
    @param {number} cost - 创意花费
    @param {number} days - 天数阈值，默认5天
    @param {number} cost_threshold - 花费阈值，默认3元
    @return {boolean} - 是否需要保护该创意
    """
    # 检查日期条件
    creative_date = parse_creative_date(creative_name)
    if not creative_date:
        return False
    
    is_recent = (datetime.now() - creative_date).days <= days
    is_low_cost = float(cost) <= cost_threshold
    
    # 只有同时满足最近日期和低花费条件才保护
    return is_recent and is_low_cost

def is_recent_creative(creative_name, days=5):
    """
    判断创意是否在指定天数内
    @param {string} creative_name - 创意名称
    @param {number} days - 天数
    @return {boolean} - 是否在指定天数内
    """
    creative_date = parse_creative_date(creative_name)
    if not creative_date:
        return False
    return (datetime.now() - creative_date).days <= days


def delete_creatives(uid, creative_ids):
    """批量删除指定的创意"""
    if not creative_ids:
        return
    
    # 设置删除创意的API地址和请求头
    url = 'http://game.raisedsun.com/prod-api/ad/account/cyDelBatch'
    headers = {
        'Accept': 'application/json, text/plain, */*',
        'Authorization': AUTHORIZATION,
        'Content-Type': 'application/json;charset=UTF-8',
        'Cookie': f'Admin-Token={AUTHORIZATION}',
        'Origin': 'http://game.raisedsun.com',
        'Referer': f'http://game.raisedsun.com/cy/uid/{uid}'
    }
    
    data = {
        "accountId": str(uid),
        "cyIds": creative_ids
    }
    
    try:
        response = requests.post(url, headers=headers, json=data, verify=False)
        if response.status_code == 200:
            print(f"UID: {uid} - 成功删除 {len(creative_ids)} 个创意")
        else:
            print(f"UID: {uid} - 删除创意失败")
    except Exception as e:
        print(f"UID: {uid} - 删除创意出错: {str(e)}")

def update_ad_roi(uid, ad_group_id):
    """更新广告组的ROI值"""
    # 获取广告组ROI
    group_url = 'http://game.raisedsun.com/prod-api/ad/account/groupList'
    params = {
        'uid': uid,
        'pageNum': 1,
        'pageSize': 500,
        'name': str(ad_group_id),
        'startDate': start_date_str,
        'endDate': end_date_str,
        'operationStatus': 3,
        'isEmpty': 'false'
    }
    
    headers = {
        'Accept': 'application/json, text/plain, */*',
        'Authorization': AUTHORIZATION,
        'Cookie': f'Admin-Token={AUTHORIZATION}',
        'Referer': f'http://game.raisedsun.com/ad/uid/{uid}'
    }
    
    try:
        response = requests.get(group_url, params=params, headers=headers, verify=False)
        if response.status_code == 200:
            data = response.json()
            if 'rows' in data and data['rows']:
                current_roi = float(data['rows'][0].get('deepConversionWorthRate', 0))
                new_roi = current_roi + 0.01
                
                # 更新ROI
                update_url = f'http://game.raisedsun.com/prod-api/ad/account/updateAdRoi'
                params = {
                    'accountId': str(uid),
                    'adGroupId': str(ad_group_id),
                    'roi': f"{new_roi:.2f}"
                }
                
                update_response = requests.get(update_url, params=params, headers=headers, verify=False)
                if update_response.status_code == 200:
                    print(f"UID: {uid} - 广告组 {ad_group_id} ROI更新: {current_roi:.2f} -> {new_roi:.2f}")
    except Exception as e:
        print(f"UID: {uid} - 更新ROI出错: {str(e)}")

def fetch_creative_data(uid, filters, group_name):
    """获取并处理创意数据"""
    min_cost = filters['min_cost']
    min_roi = filters['min_roi']
    high_cost_threshold = filters['high_cost_threshold']
    
    print(f"\n----------------------------------------")
    print(f"UID: {uid} | 数据组: {group_name}")
    print(f"条件: 花费>={min_cost} | ROI>={min_roi} | 高花费阈值: {high_cost_threshold}")
    
    url = f'http://game.raisedsun.com/prod-api/ad/account/creativeList'
    
    params = {
        'uid': uid,
        'pageNum': 1,
        'pageSize': 500,
        'startDate': start_date_str,
        'endDate': end_date_str,
        'operationStatus': 3,
        'isEmpty': 'false',
    }
    
    headers = {
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Authorization': AUTHORIZATION,
        'Cookie': f'Admin-Token={AUTHORIZATION}',
        'Referer': f'http://game.raisedsun.com/cy/uid/{uid}',
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
    }

    # # 打印请求参数
    # print(f"\n获取创意列表请求参数:")
    # print(f"URL: {url}")
    # print(f"参数: {params}")
    # print(f"请求头: {headers}")

    try:
        response = requests.get(url, params=params, headers=headers, verify=False)
        
        if response.status_code == 200:
            data = response.json()
            if 'rows' in data and data['rows']:
                total_creatives = len(data['rows'])
                filtered_creatives = []
                unfiltered_creatives = []
                creative_ids_to_delete = []
                must_delete_creatives = []  # 新增：ROI < 0.5 必须删除的创意
                high_cost_creatives = []
                
                for item in data['rows']:
                    dynamic_creative_id = item.get('dynamicCreativeId', '未知')
                    dynamic_creative_name = item.get('dynamicCreativeName', '')
                    cost = item.get('cost', '0')
                    roi = item.get('miniGameAdMonetizationRoi', '0')
                    ad_group_id = item.get('adgroupId', '未知')
                    
                    # 转换cost为浮点数，移除所有千分位分隔符
                    if isinstance(cost, str):
                        cost = cost.replace(',', '')
                    try:
                        cost = float(str(cost))
                    except (ValueError, TypeError):
                        cost = 0
                    
                    # 转换roi为浮点数
                    if isinstance(roi, str):
                        roi = roi.replace(',', '')
                    try:
                        roi = float(roi)
                    except (ValueError, TypeError):
                        roi = 0
                    
                    # 创建创意数据字典
                    creative_data = {
                        'creative_id': dynamic_creative_id,
                        'creative_name': dynamic_creative_name,
                        'cost': cost,
                        'roi': roi,
                        'ad_group_id': ad_group_id
                    }
                    
                    # 判断是否需要保护该创意（基于日期和花费）
                    if should_protect_creative(dynamic_creative_name, cost):
                        filtered_creatives.append(creative_data)
                        continue

                    # 首先检查是否是必须删除的创意（ROI < 0.5）
                    if roi < 0.5 and dynamic_creative_id != '未知':
                        must_delete_creatives.append(creative_data)
                        creative_ids_to_delete.append(int(dynamic_creative_id))
                    # 其他创意按原有逻辑分类
                    elif cost >= min_cost and roi >= min_roi:
                        filtered_creatives.append(creative_data)
                    else:
                        if cost > high_cost_threshold and roi < min_roi:
                            high_cost_creatives.append(creative_data)
                        else:
                            unfiltered_creatives.append(creative_data)
                            # 只有创意数量>50时才添加到待删除列表
                            if dynamic_creative_id != '未知' and total_creatives > 10:
                                creative_ids_to_delete.append(int(dynamic_creative_id))
                
                # 输出统计信息
                print(f"创意总数: {total_creatives}")
                print(f"├─ 合格创意: {len(filtered_creatives)}")
                print(f"├─ 不合格创意: {len(unfiltered_creatives)}")
                print(f"├─ ROI<0.5必删: {len(must_delete_creatives)}")
                print(f"└─ 高花费低ROI: {len(high_cost_creatives)}")
                
                # 如果所有创意都不合格，保留消耗最高的创意
                if len(filtered_creatives) == 0:
                    # 合并所有不合格创意（包括ROI<0.5的创意）
                    all_unqualified_creatives = unfiltered_creatives + must_delete_creatives

                    if len(all_unqualified_creatives) > 0:
                        # 找出消耗最高的创意
                        highest_cost_creative = max(all_unqualified_creatives, key=lambda x: x['cost'])
                        print(f"\n所有创意都不合格，保留消耗最高的创意 (ID: {highest_cost_creative['creative_id']}, 消耗: {highest_cost_creative['cost']}, ROI: {highest_cost_creative['roi']})")

                        # 从待删除列表中移除该创意
                        if int(highest_cost_creative['creative_id']) in creative_ids_to_delete:
                            creative_ids_to_delete.remove(int(highest_cost_creative['creative_id']))
                
                # 处理高花费但低ROI的创意
                if high_cost_creatives:
                    print(f"\n正在处理 {len(high_cost_creatives)} 个高花费低ROI创意...")
                    for creative in high_cost_creatives:
                        update_ad_roi(uid, creative['ad_group_id'])
                
                # 执行删除操作
                if creative_ids_to_delete:
                    if len(must_delete_creatives) > 0:
                        print(f"\n删除 {len(must_delete_creatives)} 个 ROI<0.5 的创意...")
                    if total_creatives > 50:
                        print(f"删除 {len(creative_ids_to_delete) - len(must_delete_creatives)} 个其他不合格创意...")
                    delete_creatives(uid, creative_ids_to_delete)
                
            else:
                print("未找到创意数据")
        else:
            print(f"获取数据失败: {response.status_code}")
            
    except Exception as e:
        print(f"处理出错: {str(e)}")



if __name__ == "__main__":
    print("=== 开始处理创意数据 ===")
    
    # 加载配置文件获取API URL
    config = load_config()
    api_url = config["api_url"]
    
    # 获取authorization token
    auth_headers = {
        'Authorization': 'your_secret_token_123'
    }
    auth_response = requests.get('http://124.221.208.141:5000/get/tfpt', headers=auth_headers)
    AUTHORIZATION = auth_response.text
    
    # 计算最近7天的日期范围
    end_date = datetime.today()
    start_date = end_date - timedelta(days=7)
    start_date_str = start_date.strftime('%Y-%m-%d')
    end_date_str = end_date.strftime('%Y-%m-%d')
    
    # 通过API获取配置
    print("📋 正在获取筛选配置...")
    result = get_all_filter_configs(api_url)
    
    # 检查API响应
    if result.get("code") != 20000 or not result.get("data"):
        print("❌ 获取配置失败:")
        print(f"   错误代码: {result.get('code')}")
        print(f"   错误信息: {result.get('message')}")
        sys.exit(1)
    
    # 提取启用的配置
    configs = [c for c in result.get("data", []) if c.get("enabled")]
    print(f"✅ 成功获取 {len(configs)} 个启用的配置组")
    
    # 处理每个配置组
    for config in configs:
        group_name = config.get('group_name', '')
        uids = config.get('uids', [])

        # 构建过滤条件
        filters = {
            'min_cost': config.get('min_cost', 0),
            'min_roi': config.get('min_MonetizationRoi', 0),
            'high_cost_threshold': config.get('high_cost_threshold', 50)  # 默认值
        }

        for uid in uids:
            try:
                fetch_creative_data(uid, filters, group_name)
            except Exception as e:
                print(f"UID: {uid} - 处理出错: {str(e)}")
                continue

    print(f"\n=== 处理完成 ===")
