import sqlite3
from datetime import datetime

DB_PATH = "creative_data.db"

def get_table_columns(cursor, table_name):
    """获取表的所有列名"""
    cursor.execute(f"PRAGMA table_info({table_name})")
    return [column[1] for column in cursor.fetchall()]

def ensure_column_exists(cursor, table_name, column_name, column_type):
    """确保指定列存在，如果不存在则添加"""
    existing_columns = get_table_columns(cursor, table_name)
    if column_name not in existing_columns:
        try:
            cursor.execute(f'ALTER TABLE {table_name} ADD COLUMN {column_name} {column_type}')
            print(f"✅ 已自动添加列: {column_name} ({column_type})")
            return True
        except sqlite3.OperationalError as e:
            print(f"❌ 添加列 {column_name} 失败: {e}")
            return False
    return False

def init_db():
    """初始化数据库，自动检测并添加缺失的列"""
    conn = sqlite3.connect(DB_PATH)
    c = conn.cursor()
    
    # 创建基础表结构
    c.execute('''
        CREATE TABLE IF NOT EXISTS creative (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            uid TEXT,
            accountId INTEGER,
            adgroupId INTEGER,
            adgroupName TEXT,
            dynamicCreativeId INTEGER,
            configuredStatus TEXT,
            brandName TEXT,
            imageId INTEGER,
            dynamicCreativeName TEXT,
            pageType TEXT,
            systemStatusCn TEXT,
            miniGameAdMonetizationRoi TEXT,
            miniGameIncomeRoi1 TEXT,
            cost TEXT,
            amount TEXT,
            miniGameFirstDayAdMonetizationAmount TEXT,
            miniGameAdMonetizationAmount TEXT,
            miniGameRegisterCost TEXT,
            miniGameRetentionD1Rate TEXT,
            creativeTemplateId INTEGER,
            cpc TEXT,
            videoId INTEGER,
            type TEXT,
            content TEXT,
            url TEXT,
            created_at TEXT,
            raw_json TEXT,
            filter_version TEXT,
            filter_name TEXT,
            config_uids TEXT,
            group_name TEXT
        )
    ''')
    
    # 定义需要确保存在的列（列名: 数据类型）
    required_columns = {
        'fileId': 'INTEGER',
        'fileId_data': 'TEXT',
        'flag': 'INTEGER',
        'saved_to_library': 'INTEGER',
        'saved_at': 'TEXT',
        'used_by_uid': 'TEXT'
    }
    
    # 自动检测并添加缺失的列
    added_columns = []
    for column_name, column_type in required_columns.items():
        if ensure_column_exists(c, 'creative', column_name, column_type):
            added_columns.append(column_name)
    
    if added_columns:
        print(f"🔧 数据库结构已更新，新增列: {', '.join(added_columns)}")
    
    # 创建唯一索引（如果不存在）
    try:
        c.execute('CREATE UNIQUE INDEX IF NOT EXISTS idx_group_dynamic ON creative(group_name, dynamicCreativeId)')
    except sqlite3.OperationalError:
        pass  # 索引已存在
    
    conn.commit()
    conn.close()
    
    if added_columns:
        return added_columns
    return None

def insert_creative(uid, creative_row, filter_version=None, filter_name=None, config_uids=None, group_name=None):
    conn = sqlite3.connect(DB_PATH)
    c = conn.cursor()
    dynamicCreativeId = creative_row.get("dynamicCreativeId")
    # 检查是否已存在
    c.execute('SELECT id, url, saved_to_library, saved_at FROM creative WHERE group_name=? AND dynamicCreativeId=?', (group_name, dynamicCreativeId))
    row = c.fetchone()
    if row:
        # 存在则更新，但保留已有的url和保存状态字段
        existing_url = row[1]  # 保留已有的url
        existing_saved_to_library = row[2]  # 保留已有的saved_to_library
        existing_saved_at = row[3]  # 保留已有的saved_at
        
        # 只有当API返回的url不为空时才更新url字段
        new_url = creative_row.get("url") if creative_row.get("url") else existing_url
        
        c.execute('''
            UPDATE creative SET
                uid=?, accountId=?, adgroupId=?, adgroupName=?, configuredStatus=?, brandName=?, imageId=?, dynamicCreativeName=?, pageType=?, systemStatusCn=?, miniGameAdMonetizationRoi=?, miniGameIncomeRoi1=?, cost=?, amount=?, miniGameFirstDayAdMonetizationAmount=?, miniGameAdMonetizationAmount=?, miniGameRegisterCost=?, miniGameRetentionD1Rate=?, creativeTemplateId=?, cpc=?, videoId=?, type=?, content=?, url=?, created_at=?, raw_json=?, filter_version=?, filter_name=?, config_uids=?
            WHERE group_name=? AND dynamicCreativeId=?
        ''', (
            uid,
            creative_row.get("accountId"),
            creative_row.get("adgroupId"),
            creative_row.get("adgroupName"),
            creative_row.get("configuredStatus"),
            creative_row.get("brandName"),
            creative_row.get("imageId"),
            creative_row.get("dynamicCreativeName"),
            creative_row.get("pageType"),
            creative_row.get("systemStatusCn"),
            creative_row.get("miniGameAdMonetizationRoi"),
            creative_row.get("miniGameIncomeRoi1"),
            creative_row.get("cost"),
            creative_row.get("amount"),
            creative_row.get("miniGameFirstDayAdMonetizationAmount"),
            creative_row.get("miniGameAdMonetizationAmount"),
            creative_row.get("miniGameRegisterCost"),
            creative_row.get("miniGameRetentionD1Rate"),
            creative_row.get("creativeTemplateId"),
            creative_row.get("cpc"),
            creative_row.get("videoId"),
            creative_row.get("type"),
            creative_row.get("content"),
            new_url,  # 使用保护后的url
            datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            str(creative_row),
            filter_version,
            filter_name,
            config_uids,
            group_name,
            dynamicCreativeId
        ))
        
        # 如果原来有保存状态，需要单独更新这些字段（因为UPDATE语句中没有包含）
        if existing_saved_to_library is not None or existing_saved_at is not None:
            c.execute('UPDATE creative SET saved_to_library=?, saved_at=? WHERE group_name=? AND dynamicCreativeId=?',
                     (existing_saved_to_library, existing_saved_at, group_name, dynamicCreativeId))
    else:
        # 不存在则插入
        c.execute('''
            INSERT INTO creative (uid, accountId, adgroupId, adgroupName, dynamicCreativeId, configuredStatus, brandName, imageId, dynamicCreativeName, pageType, systemStatusCn, miniGameAdMonetizationRoi, miniGameIncomeRoi1, cost, amount, miniGameFirstDayAdMonetizationAmount, miniGameAdMonetizationAmount, miniGameRegisterCost, miniGameRetentionD1Rate, creativeTemplateId, cpc, videoId, type, content, url, created_at, raw_json, filter_version, filter_name, config_uids, group_name)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            uid,
            creative_row.get("accountId"),
            creative_row.get("adgroupId"),
            creative_row.get("adgroupName"),
            dynamicCreativeId,
            creative_row.get("configuredStatus"),
            creative_row.get("brandName"),
            creative_row.get("imageId"),
            creative_row.get("dynamicCreativeName"),
            creative_row.get("pageType"),
            creative_row.get("systemStatusCn"),
            creative_row.get("miniGameAdMonetizationRoi"),
            creative_row.get("miniGameIncomeRoi1"),
            creative_row.get("cost"),
            creative_row.get("amount"),
            creative_row.get("miniGameFirstDayAdMonetizationAmount"),
            creative_row.get("miniGameAdMonetizationAmount"),
            creative_row.get("miniGameRegisterCost"),
            creative_row.get("miniGameRetentionD1Rate"),
            creative_row.get("creativeTemplateId"),
            creative_row.get("cpc"),
            creative_row.get("videoId"),
            creative_row.get("type"),
            creative_row.get("content"),
            creative_row.get("url"),
            datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            str(creative_row),
            filter_version,
            filter_name,
            config_uids,
            group_name
        ))
    conn.commit()
    conn.close()

def delete_all():
    conn = sqlite3.connect(DB_PATH)
    c = conn.cursor()
    c.execute('DELETE FROM creative')
    conn.commit()
    conn.close()

def show_all():
    conn = sqlite3.connect(DB_PATH)
    c = conn.cursor()
    for row in c.execute('SELECT * FROM creative'):
        print(row)
    conn.close()

def show_group_stats():
    conn = sqlite3.connect(DB_PATH)
    c = conn.cursor()
    c.execute('SELECT group_name, COUNT(DISTINCT dynamicCreativeId), config_uids FROM creative GROUP BY group_name')
    rows = c.fetchall()
    print("group_name 分组统计:")
    for group_name, count, config_uids in rows:
        print(f"{group_name} (config_uids: {config_uids}): {count} 个 dynamicCreativeId")
    conn.close()

def show_group_details(group_name):
    conn = sqlite3.connect(DB_PATH)
    c = conn.cursor()
    c.execute('SELECT dynamicCreativeId, config_uids FROM creative WHERE group_name=?', (group_name,))
    rows = c.fetchall()
    if not rows:
        print(f"未找到 group_name = {group_name} 的数据")
    else:
        dynamic_ids = [str(row[0]) for row in rows]
        config_uids = rows[0][1] if rows else ''
        print(f"\n[{group_name}] (UID: {config_uids}) 共{len(dynamic_ids)}条创意：")
        print(f"dynamicCreativeId: {', '.join(dynamic_ids)}")
    conn.close()

# ensure_columns函数已删除，因为相关字段未被使用

def get_config_uids(group_name):
    conn = sqlite3.connect(DB_PATH)
    c = conn.cursor()
    c.execute('SELECT config_uids FROM creative WHERE group_name=? LIMIT 1', (group_name,))
    row = c.fetchone()
    conn.close()
    if row and row[0]:
        return [int(uid) for uid in row[0].split(',') if uid.strip()]
    return []

# mark_creatives_used函数已删除，因为相关字段未被使用

def update_creative_fileid(creative_id, file_id, file_id_data, flag=None):
    """更新创意的fileId、fileId_data和flag字段"""
    conn = sqlite3.connect(DB_PATH)
    c = conn.cursor()
    if flag is not None:
        c.execute('UPDATE creative SET fileId=?, fileId_data=?, flag=? WHERE id=?', (file_id, file_id_data, flag, creative_id))
    else:
        c.execute('UPDATE creative SET fileId=?, fileId_data=? WHERE id=?', (file_id, file_id_data, creative_id))
    conn.commit()
    conn.close()

def get_creatives_by_url(url):
    """根据url获取创意记录"""
    conn = sqlite3.connect(DB_PATH)
    c = conn.cursor()
    c.execute('SELECT id, url FROM creative WHERE url=?', (url,))
    rows = c.fetchall()
    conn.close()
    return rows

def get_creatives_without_fileid():
    """获取没有fileId的创意记录"""
    conn = sqlite3.connect(DB_PATH)
    c = conn.cursor()
    c.execute('SELECT id, url FROM creative WHERE url IS NOT NULL AND url != "" AND url != "上传失败" AND (fileId IS NULL OR fileId = "")')
    rows = c.fetchall()
    conn.close()
    return rows

def get_unused_fileids_by_group(group_name, limit=None):
    """根据group_name获取未使用的fileId和fileId_data"""
    conn = sqlite3.connect(DB_PATH)
    c = conn.cursor()
    
    # 查询指定group_name中未被使用的fileId
    query = '''
        SELECT fileId, fileId_data 
        FROM creative 
        WHERE group_name=? 
        AND fileId IS NOT NULL 
        AND fileId != "" 
        AND (used_by_uid IS NULL OR used_by_uid = "")
    '''
    
    if limit:
        query += f' LIMIT {limit}'
    
    c.execute(query, (group_name,))
    rows = c.fetchall()
    conn.close()
    
    return [(row[0], row[1]) for row in rows]

def get_all_fileids_by_group(group_name, limit=None):
    """根据group_name获取所有fileId和fileId_data（不限制使用状态，排除flag=2）"""
    conn = sqlite3.connect(DB_PATH)
    c = conn.cursor()

    # 查询指定group_name中所有的fileId，排除flag=2的记录
    query = '''
        SELECT fileId, fileId_data
        FROM creative
        WHERE group_name=?
        AND fileId IS NOT NULL
        AND fileId != ""
        AND (flag IS NULL OR flag != 2)
    '''

    if limit:
        query += f' LIMIT {limit}'

    c.execute(query, (group_name,))
    rows = c.fetchall()
    conn.close()

    return [(row[0], row[1]) for row in rows]

def mark_fileid_used(group_name, file_id, used_by_uid):
    """标记指定group_name中的fileId为已使用"""
    conn = sqlite3.connect(DB_PATH)
    c = conn.cursor()
    c.execute(
        'UPDATE creative SET used_by_uid=? WHERE group_name=? AND fileId=?',
        (used_by_uid, group_name, file_id)
    )
    conn.commit()
    conn.close()

def reset_fileid_usage(group_name=None):
    """重置fileId使用状态，可选择指定group_name"""
    conn = sqlite3.connect(DB_PATH)
    c = conn.cursor()
    
    if group_name:
        c.execute('UPDATE creative SET used_by_uid=NULL WHERE group_name=?', (group_name,))
        # 同时清理新的使用记录表
        c.execute('DELETE FROM fileid_usage WHERE group_name=?', (group_name,))
    else:
        c.execute('UPDATE creative SET used_by_uid=NULL')
        # 同时清理新的使用记录表
        c.execute('DELETE FROM fileid_usage')
    
    conn.commit()
    conn.close()

def record_fileid_usage(group_name, file_id, uid):
    """记录fileId使用情况"""
    conn = sqlite3.connect(DB_PATH)
    c = conn.cursor()
    
    try:
        c.execute(
            'INSERT OR REPLACE INTO fileid_usage (group_name, file_id, uid, used_at) VALUES (?, ?, ?, ?)',
            (group_name, file_id, uid, datetime.now().isoformat())
        )
        conn.commit()
        return True
    except Exception as e:
        print(f"记录fileId使用失败: {e}")
        return False
    finally:
        conn.close()

def check_fileid_used_by_uid(group_name, file_id, uid):
    """检查指定UID是否已使用过该fileId"""
    conn = sqlite3.connect(DB_PATH)
    c = conn.cursor()
    
    c.execute(
        'SELECT COUNT(*) FROM fileid_usage WHERE group_name=? AND file_id=? AND uid=?',
        (group_name, file_id, uid)
    )
    count = c.fetchone()[0]
    conn.close()
    
    return count > 0

def get_unused_fileids_for_uid(group_name, uid, limit=None):
    """获取指定UID未使用过的fileId列表（排除flag=2）"""
    conn = sqlite3.connect(DB_PATH)
    c = conn.cursor()

    # 查询该UID未使用过的fileId，排除flag=2的记录
    query = '''
        SELECT c.fileId, c.fileId_data
        FROM creative c
        WHERE c.group_name=?
        AND c.fileId IS NOT NULL
        AND c.fileId != ""
        AND (c.flag IS NULL OR c.flag != 2)
        AND NOT EXISTS (
            SELECT 1 FROM fileid_usage fu
            WHERE fu.group_name=? AND fu.file_id=c.fileId AND fu.uid=?
        )
    '''

    if limit:
        query += f' LIMIT {limit}'

    c.execute(query, (group_name, group_name, uid))
    rows = c.fetchall()
    conn.close()

    return [(row[0], row[1]) for row in rows]

def get_fileid_usage_stats(group_name=None):
    """获取fileId使用统计信息"""
    conn = sqlite3.connect(DB_PATH)
    c = conn.cursor()
    
    if group_name:
        c.execute(
            'SELECT file_id, COUNT(DISTINCT uid) as uid_count FROM fileid_usage WHERE group_name=? GROUP BY file_id',
            (group_name,)
        )
    else:
        c.execute(
            'SELECT group_name, file_id, COUNT(DISTINCT uid) as uid_count FROM fileid_usage GROUP BY group_name, file_id'
        )
    
    rows = c.fetchall()
    conn.close()
    
    return rows

def mark_fileids_used_for_uid(file_ids, uid, group_name):
    """批量标记多个fileId为指定UID已使用"""
    conn = sqlite3.connect(DB_PATH)
    c = conn.cursor()
    
    success_count = 0
    for file_id in file_ids:
        try:
            c.execute(
                'INSERT OR REPLACE INTO fileid_usage (group_name, file_id, uid, used_at) VALUES (?, ?, ?, ?)',
                (group_name, file_id, uid, datetime.now().isoformat())
            )
            success_count += 1
        except Exception as e:
            print(f"标记fileId {file_id} 使用失败: {e}")
    
    conn.commit()
    conn.close()
    
    return success_count

def add_column_if_not_exists(column_name, column_type, default_value=None):
    """通用函数：添加列如果不存在"""
    conn = sqlite3.connect(DB_PATH)
    c = conn.cursor()
    
    existing_columns = get_table_columns(c, 'creative')
    if column_name not in existing_columns:
        try:
            alter_sql = f'ALTER TABLE creative ADD COLUMN {column_name} {column_type}'
            if default_value is not None:
                alter_sql += f' DEFAULT {default_value}'
            c.execute(alter_sql)
            conn.commit()
            print(f"✅ 成功添加新列: {column_name} ({column_type})")
            return True
        except sqlite3.OperationalError as e:
            print(f"❌ 添加列 {column_name} 失败: {e}")
            return False
        finally:
            conn.close()
    else:
        conn.close()
        print(f"ℹ️ 列 {column_name} 已存在，跳过添加")
        return False

def create_fileid_usage_table():
    """创建fileId使用记录表"""
    conn = sqlite3.connect(DB_PATH)
    c = conn.cursor()
    
    # 创建fileId使用记录表
    c.execute('''
        CREATE TABLE IF NOT EXISTS fileid_usage (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            group_name TEXT NOT NULL,
            file_id INTEGER NOT NULL,
            uid INTEGER NOT NULL,
            used_at TEXT NOT NULL,
            UNIQUE(group_name, file_id, uid)
        )
    ''')
    
    conn.commit()
    conn.close()

def check_and_update_schema():
    """检查并更新数据库架构到最新版本"""
    print("🔍 检查数据库架构...")
    
    # 定义当前版本需要的所有列
    schema_version = {
        'fileId': 'INTEGER',
        'fileId_data': 'TEXT',
        'flag': 'INTEGER',
        'saved_to_library': 'INTEGER',
        'saved_at': 'TEXT',
        'used_by_uid': 'TEXT',
        # 未来可以在这里添加新的列定义
        # 'new_column': 'TEXT DEFAULT NULL'
    }
    
    conn = sqlite3.connect(DB_PATH)
    c = conn.cursor()
    
    existing_columns = get_table_columns(c, 'creative')
    missing_columns = []
    
    for column_name, column_type in schema_version.items():
        if column_name not in existing_columns:
            missing_columns.append((column_name, column_type))
    
    if missing_columns:
        print(f"📋 发现 {len(missing_columns)} 个缺失的列，正在添加...")
        for column_name, column_type in missing_columns:
            ensure_column_exists(c, 'creative', column_name, column_type)
        conn.commit()
        print("✅ 数据库架构更新完成")
    else:
        print("✅ 数据库架构已是最新版本")
    
    conn.close()
    
    # 创建fileId使用记录表
    create_fileid_usage_table()
    print("✅ fileId使用记录表已就绪")
    
    return len(missing_columns) > 0

if __name__ == "__main__":
    import sys
    init_db()
    if len(sys.argv) == 2 and sys.argv[1] == "show":
        show_all()
    elif len(sys.argv) == 2 and sys.argv[1] == "clear":
        delete_all()
    elif len(sys.argv) == 2 and sys.argv[1] == "groupstats":
        show_group_stats()
    else:
        print("用法: python creative_db.py show|clear|groupstats")