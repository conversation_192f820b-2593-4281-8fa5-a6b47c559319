#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import os

# 获取脚本所在目录
script_dir = os.path.dirname(os.path.abspath(__file__))
db_path = os.path.join(script_dir, "creative_data.db")

print(f"脚本目录: {script_dir}")
print(f"数据库路径: {db_path}")
print(f"数据库文件存在: {os.path.exists(db_path)}")

try:
    conn = sqlite3.connect(db_path)
    c = conn.cursor()
    
    # 检查表是否存在
    c.execute("SELECT name FROM sqlite_master WHERE type='table'")
    tables = [row[0] for row in c.fetchall()]
    print(f"数据库中的表: {tables}")
    
    if 'creative' in tables:
        print("✅ creative表存在")
        # 检查表结构
        c.execute("PRAGMA table_info(creative)")
        columns = c.fetchall()
        print(f"creative表有 {len(columns)} 个字段")
        
        # 检查数据
        c.execute("SELECT COUNT(*) FROM creative")
        count = c.fetchone()[0]
        print(f"creative表中有 {count} 条记录")
        
        # 测试查询
        c.execute('SELECT accountId, imageId, brandName, dynamicCreativeId FROM creative WHERE imageId IS NOT NULL AND (url IS NULL OR url = "") LIMIT 5')
        rows = c.fetchall()
        print(f"找到 {len(rows)} 条需要处理的记录")
        
    else:
        print("❌ creative表不存在")
        
    conn.close()
    print("✅ 数据库连接测试成功")
    
except Exception as e:
    print(f"❌ 数据库连接测试失败: {e}")
