#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
初始化筛选配置数据脚本
将预定义的筛选配置写入数据库
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.utils.ad_database import ad_database

# 预定义的筛选配置数据
FILTER_CONFIGS = [
    {
        "group_name": "group1",
        "filter_version": [],
        "filter_name": ["贪吃蛇跑酷作战", "贪吃蛇跑酷进化"],
        "min_cost": 500,
        "min_MonetizationRoi": 0.1,
        "appId": "wxea014b426023677b",
        "enabled": True
    },
    {
        "group_name": "group2",
        "filter_version": [],
        "filter_name": ["狗头"],
        "min_cost": 800,
        "min_MonetizationRoi": 0.1,
        "appId": "wxea014b426023677b",
        "enabled": True
    },
    {
        "group_name": "group3",
        "filter_version": [],
        "filter_name": ["画线", "拯救"],
        "min_cost": 5,
        "min_MonetizationRoi": 0.1,
        "appId": "wxea014b426023677b",
        "enabled": False
    }
]

async def init_filter_configs():
    """初始化筛选配置数据"""
    try:
        print("开始初始化筛选配置数据...")
        
        # 初始化数据库
        await ad_database.init_database()
        print("数据库初始化完成")
        
        # 插入配置数据
        success_count = 0
        for config in FILTER_CONFIGS:
            try:
                # 检查配置是否已存在
                existing_configs = await ad_database.get_filter_configs(search=config["group_name"])
                
                if existing_configs["total"] > 0:
                    print(f"配置组 '{config['group_name']}' 已存在，跳过...")
                    continue
                
                # 创建新配置
                success = await ad_database.create_filter_config(config)
                if success:
                    success_count += 1
                    print(f"✓ 成功创建配置组: {config['group_name']}")
                else:
                    print(f"✗ 创建配置组失败: {config['group_name']}")
                    
            except Exception as e:
                print(f"✗ 创建配置组 '{config['group_name']}' 时发生错误: {e}")
        
        print(f"\n初始化完成！成功创建 {success_count} 个配置组")
        
        # 验证数据
        print("\n验证配置数据...")
        all_configs = await ad_database.get_filter_configs()
        print(f"数据库中共有 {all_configs['total']} 个筛选配置组")
        
        for item in all_configs['items']:
            status = "启用" if item['enabled'] else "禁用"
            print(f"- {item['group_name']}: {len(item['filter_name'])} 个筛选名称, 最低花费 {item['min_cost']}, 状态: {status}")
        
    except Exception as e:
        print(f"初始化失败: {e}")
        return False
    
    return True

async def main():
    """主函数"""
    print("=" * 50)
    print("筛选配置数据初始化脚本")
    print("=" * 50)
    
    success = await init_filter_configs()
    
    if success:
        print("\n🎉 初始化成功！")
    else:
        print("\n❌ 初始化失败！")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
