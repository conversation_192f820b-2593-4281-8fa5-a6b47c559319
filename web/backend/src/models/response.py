#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from pydantic import BaseModel
from typing import Any, Optional, List, Generic, TypeVar

T = TypeVar('T')

class ApiResponse(BaseModel, Generic[T]):
    """统一API响应模型"""
    code: int = 20000
    message: str = "success"
    data: Optional[T] = None

class PaginatedResponse(BaseModel, Generic[T]):
    """分页响应模型"""
    total: int
    items: List[T]

class TransactionItem(BaseModel):
    """交易项目模型"""
    order_no: str
    timestamp: int
    username: str
    price: float
    status: str

class ArticleItem(BaseModel):
    """文章项目模型"""
    id: int
    timestamp: int
    author: str
    reviewer: str
    title: str
    content_short: str
    forecast: float
    importance: int
    type: str
    status: str
    display_time: str
    comment_disabled: bool
    pageviews: int
    platforms: List[str]

class RoleItem(BaseModel):
    """角色项目模型"""
    key: str
    name: str
    description: str
    routes: List[str]

class UserSearchItem(BaseModel):
    """用户搜索项目模型"""
    name: str
