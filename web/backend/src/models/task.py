#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from pydantic import BaseModel, Field
from typing import Optional, Dict, Any, List
from datetime import datetime
from enum import Enum

class TaskStatus(str, Enum):
    """任务状态枚举"""
    PENDING = "pending"      # 等待中
    RUNNING = "running"      # 运行中
    SUCCESS = "success"      # 成功
    FAILED = "failed"        # 失败
    PAUSED = "paused"        # 暂停

class TaskType(str, Enum):
    """任务类型枚举"""
    CRON = "cron"           # 定时任务
    INTERVAL = "interval"   # 间隔任务
    DATE = "date"           # 一次性任务

class TaskBase(BaseModel):
    """任务基础模型"""
    name: str = Field(..., description="任务名称")
    description: Optional[str] = Field(None, description="任务描述")
    script_path: str = Field(..., description="脚本路径")
    task_type: TaskType = Field(..., description="任务类型")
    cron_expression: Optional[str] = Field(None, description="Cron表达式")
    interval_seconds: Optional[int] = Field(None, description="间隔秒数")
    run_date: Optional[datetime] = Field(None, description="运行日期")
    enabled: bool = Field(default=True, description="是否启用")
    max_instances: int = Field(default=1, description="最大实例数")
    timeout: Optional[int] = Field(None, description="超时时间(秒)")
    env_vars: Optional[Dict[str, str]] = Field(default_factory=dict, description="环境变量")

class TaskCreate(TaskBase):
    """创建任务模型"""
    pass

class TaskUpdate(BaseModel):
    """更新任务模型"""
    name: Optional[str] = None
    description: Optional[str] = None
    script_path: Optional[str] = None
    task_type: Optional[TaskType] = None
    cron_expression: Optional[str] = None
    interval_seconds: Optional[int] = None
    run_date: Optional[datetime] = None
    enabled: Optional[bool] = None
    max_instances: Optional[int] = None
    timeout: Optional[int] = None
    env_vars: Optional[Dict[str, str]] = None

class TaskResponse(TaskBase):
    """任务响应模型"""
    id: int
    status: TaskStatus
    last_run_time: Optional[datetime]
    next_run_time: Optional[datetime]
    run_count: int
    success_count: int
    fail_count: int
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

class TaskLogBase(BaseModel):
    """任务日志基础模型"""
    task_id: int
    status: TaskStatus
    start_time: datetime
    end_time: Optional[datetime] = None
    output: Optional[str] = None
    error: Optional[str] = None
    exit_code: Optional[int] = None

class TaskLogResponse(TaskLogBase):
    """任务日志响应模型"""
    id: int
    duration: Optional[float] = None  # 执行时长(秒)
    
    class Config:
        from_attributes = True

class TaskExecuteRequest(BaseModel):
    """立即执行任务请求模型"""
    env_vars: Optional[Dict[str, str]] = Field(default_factory=dict, description="临时环境变量")

class TaskStatsResponse(BaseModel):
    """任务统计响应模型"""
    total_tasks: int
    enabled_tasks: int
    running_tasks: int
    success_rate: float
    total_executions: int
