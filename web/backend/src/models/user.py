#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from pydantic import BaseModel, EmailStr
from typing import Optional, List
from datetime import datetime

class UserBase(BaseModel):
    """用户基础模型"""
    username: str
    email: Optional[EmailStr] = None
    role: str = "user"
    avatar: Optional[str] = None
    introduction: Optional[str] = None

class UserCreate(UserBase):
    """创建用户模型"""
    password: str

class UserUpdate(BaseModel):
    """更新用户模型"""
    username: Optional[str] = None
    email: Optional[EmailStr] = None
    role: Optional[str] = None
    avatar: Optional[str] = None
    introduction: Optional[str] = None
    password: Optional[str] = None

class UserLogin(BaseModel):
    """用户登录模型"""
    username: str
    password: str

class UserResponse(UserBase):
    """用户响应模型"""
    id: int
    roles: List[str]
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True

class Token(BaseModel):
    """Token响应模型"""
    access_token: str
    token_type: str = "bearer"

class TokenData(BaseModel):
    """Token数据模型"""
    user_id: Optional[str] = None
