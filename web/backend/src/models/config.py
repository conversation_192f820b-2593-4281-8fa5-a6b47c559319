#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime

class FilterConfigBase(BaseModel):
    """筛选配置基础模型"""
    filter_version: List[str] = Field(default_factory=list, description="筛选版本列表")
    filter_name: List[str] = Field(default_factory=list, description="筛选名称列表")
    min_cost: float = Field(default=0, ge=0, description="最低花费")
    min_MonetizationRoi: float = Field(default=0, ge=0, description="最低变现ROI")
    appId: str = Field(..., description="应用ID（必填）")
    labelName: List[str] = Field(default_factory=list, description="标签列表（必填）")
    enabled: bool = Field(default=True, description="是否启用")

class FilterConfigCreate(FilterConfigBase):
    """创建筛选配置模型"""
    group_name: Optional[str] = Field(None, description="配置组名称（可选，不填则自动生成）")

class FilterConfigUpdate(BaseModel):
    """更新筛选配置模型"""
    filter_version: Optional[List[str]] = None
    filter_name: Optional[List[str]] = None
    min_cost: Optional[float] = Field(None, ge=0)
    min_MonetizationRoi: Optional[float] = Field(None, ge=0)
    appId: Optional[str] = None
    labelName: Optional[List[str]] = None
    enabled: Optional[bool] = None

class FilterConfigResponse(FilterConfigBase):
    """筛选配置响应模型"""
    id: int
    group_name: str = Field(..., description="配置组名称")
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


