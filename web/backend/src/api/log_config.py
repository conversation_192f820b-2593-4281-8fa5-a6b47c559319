#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
日志优化配置管理API
"""

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel, Field
from typing import List
from ..config.settings import settings

router = APIRouter(prefix="/api/log-config", tags=["日志配置"])

class LogConfigResponse(BaseModel):
    """日志配置响应模型"""
    update_interval: float = Field(description="最小更新间隔(秒)")
    batch_size: int = Field(description="批量更新行数")
    urgent_keywords: List[str] = Field(description="紧急关键词列表")

class LogConfigUpdate(BaseModel):
    """日志配置更新模型"""
    update_interval: float = Field(ge=0.1, le=5.0, description="最小更新间隔(秒), 范围: 0.1-5.0")
    batch_size: int = Field(ge=1, le=20, description="批量更新行数, 范围: 1-20")
    urgent_keywords: List[str] = Field(description="紧急关键词列表")

@router.get("/", response_model=LogConfigResponse, summary="获取日志优化配置")
async def get_log_config():
    """获取当前的日志优化配置"""
    return LogConfigResponse(
        update_interval=settings.LOG_UPDATE_INTERVAL,
        batch_size=settings.LOG_BATCH_SIZE,
        urgent_keywords=settings.LOG_URGENT_KEYWORDS
    )

@router.put("/", response_model=LogConfigResponse, summary="更新日志优化配置")
async def update_log_config(config: LogConfigUpdate):
    """更新日志优化配置"""
    try:
        # 更新配置
        settings.LOG_UPDATE_INTERVAL = config.update_interval
        settings.LOG_BATCH_SIZE = config.batch_size
        settings.LOG_URGENT_KEYWORDS = config.urgent_keywords
        
        return LogConfigResponse(
            update_interval=settings.LOG_UPDATE_INTERVAL,
            batch_size=settings.LOG_BATCH_SIZE,
            urgent_keywords=settings.LOG_URGENT_KEYWORDS
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新配置失败: {str(e)}")

@router.post("/reset", response_model=LogConfigResponse, summary="重置日志优化配置")
async def reset_log_config():
    """重置日志优化配置为默认值"""
    try:
        # 重置为默认值
        settings.LOG_UPDATE_INTERVAL = 0.5
        settings.LOG_BATCH_SIZE = 3
        settings.LOG_URGENT_KEYWORDS = ['❌', '✅', '⚠️', '错误', '失败', '成功', '完成', '开始']
        
        return LogConfigResponse(
            update_interval=settings.LOG_UPDATE_INTERVAL,
            batch_size=settings.LOG_BATCH_SIZE,
            urgent_keywords=settings.LOG_URGENT_KEYWORDS
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"重置配置失败: {str(e)}")

@router.get("/presets", summary="获取预设配置")
async def get_log_config_presets():
    """获取预设的日志优化配置"""
    return {
        "presets": [
            {
                "name": "高性能模式",
                "description": "最大化执行速度，减少日志更新频率",
                "update_interval": 1.0,
                "batch_size": 10,
                "urgent_keywords": ['❌', '✅', '错误', '失败', '成功']
            },
            {
                "name": "平衡模式",
                "description": "平衡性能和实时性",
                "update_interval": 0.5,
                "batch_size": 3,
                "urgent_keywords": ['❌', '✅', '⚠️', '错误', '失败', '成功', '完成', '开始']
            },
            {
                "name": "实时模式",
                "description": "最大化实时性，频繁更新日志",
                "update_interval": 0.2,
                "batch_size": 1,
                "urgent_keywords": ['❌', '✅', '⚠️', '🔍', '📱', '📋', '错误', '失败', '成功', '完成', '开始', '处理']
            },
            {
                "name": "调试模式",
                "description": "适合开发调试，详细的实时日志",
                "update_interval": 0.1,
                "batch_size": 1,
                "urgent_keywords": ['❌', '✅', '⚠️', '🔍', '📱', '📋', '🎯', '📊', '错误', '失败', '成功', '完成', '开始', '处理', '查询', '获取']
            }
        ]
    }

@router.post("/apply-preset/{preset_name}", response_model=LogConfigResponse, summary="应用预设配置")
async def apply_log_config_preset(preset_name: str):
    """应用预设的日志优化配置"""
    presets = {
        "high-performance": {
            "update_interval": 1.0,
            "batch_size": 10,
            "urgent_keywords": ['❌', '✅', '错误', '失败', '成功']
        },
        "balanced": {
            "update_interval": 0.5,
            "batch_size": 3,
            "urgent_keywords": ['❌', '✅', '⚠️', '错误', '失败', '成功', '完成', '开始']
        },
        "realtime": {
            "update_interval": 0.2,
            "batch_size": 1,
            "urgent_keywords": ['❌', '✅', '⚠️', '🔍', '📱', '📋', '错误', '失败', '成功', '完成', '开始', '处理']
        },
        "debug": {
            "update_interval": 0.1,
            "batch_size": 1,
            "urgent_keywords": ['❌', '✅', '⚠️', '🔍', '📱', '📋', '🎯', '📊', '错误', '失败', '成功', '完成', '开始', '处理', '查询', '获取']
        }
    }
    
    if preset_name not in presets:
        raise HTTPException(status_code=404, detail=f"预设配置 '{preset_name}' 不存在")
    
    try:
        preset = presets[preset_name]
        settings.LOG_UPDATE_INTERVAL = preset["update_interval"]
        settings.LOG_BATCH_SIZE = preset["batch_size"]
        settings.LOG_URGENT_KEYWORDS = preset["urgent_keywords"]
        
        return LogConfigResponse(
            update_interval=settings.LOG_UPDATE_INTERVAL,
            batch_size=settings.LOG_BATCH_SIZE,
            urgent_keywords=settings.LOG_URGENT_KEYWORDS
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"应用预设配置失败: {str(e)}")
