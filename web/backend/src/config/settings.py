#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
from datetime import timedelta
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

class Settings:
    """应用配置"""
    
    # 应用基本配置
    APP_NAME: str = "Vue Element Admin API"
    APP_VERSION: str = "1.0.0"
    DEBUG: bool = True
    
    # JWT配置
    JWT_SECRET_KEY: str = os.getenv("JWT_SECRET_KEY", "your-super-secret-jwt-key-change-this-in-production")
    JWT_ALGORITHM: str = "HS256"
    JWT_ACCESS_TOKEN_EXPIRE_MINUTES: int = 24 * 60  # 24小时
    
    # 数据库配置
    DATABASE_URL: str = "sqlite:///./database/admin.db"
    
    # CORS配置
    CORS_ORIGINS: list = [
        "http://localhost:9527",
        "http://127.0.0.1:9527",
        "http://localhost:8080",
        "http://127.0.0.1:8080"
    ]
    
    # 服务器配置
    HOST: str = "0.0.0.0"
    PORT: int = 3000

    # 实时日志优化配置
    LOG_UPDATE_INTERVAL: float = 0.5  # 最小更新间隔(秒)
    LOG_BATCH_SIZE: int = 3  # 批量更新行数
    LOG_URGENT_KEYWORDS: list = ['❌', '✅', '⚠️', '错误', '失败', '成功', '完成', '开始']  # 紧急关键词

# 创建配置实例
settings = Settings()
