#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
投放数据库管理模块
负责投放数据和小游戏数据的数据库操作
"""

import os
import aiosqlite
import logging
import json
from datetime import datetime
from typing import List, Dict, Optional, Any

# 配置日志
logger = logging.getLogger(__name__)

def safe_json_loads(json_str: str, default=None):
    """安全的JSON解析函数"""
    if not json_str or not json_str.strip():
        return default if default is not None else []

    # 处理空字符串或只有引号的情况
    json_str = json_str.strip()
    if json_str in ['""', "''", '']:
        return default if default is not None else []

    try:
        result = json.loads(json_str)
        return result if result is not None else (default if default is not None else [])
    except (json.JSONDecodeError, ValueError) as e:
        logger.warning(f"JSON解析失败: {json_str}, 错误: {e}")
        return default if default is not None else []

class AdDatabase:
    """投放数据库管理类"""
    
    def __init__(self):
        # 获取项目根目录的绝对路径
        current_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = os.path.abspath(os.path.join(current_dir, '..', '..'))
        self.db_path = os.path.abspath(os.path.join(project_root, "database", "ad_platform.db"))

        # 确保数据库目录存在
        db_dir = os.path.dirname(self.db_path)
        if not os.path.exists(db_dir):
            os.makedirs(db_dir)

        self.accounts_table = "ad_accounts"
        self.games_table = "ad_games"
        self.configs_table = "filter_configs"

    def _get_connection(self):
        """获取数据库连接"""
        return aiosqlite.connect(self.db_path)
    
    async def init_database(self):
        """初始化投放数据库"""
        try:
            # 确保数据库目录存在
            os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
            
            async with aiosqlite.connect(self.db_path) as conn:
                # 创建投放账户表
                await conn.execute(f'''
                CREATE TABLE IF NOT EXISTS {self.accounts_table} (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    uid TEXT NOT NULL,
                    appId TEXT,
                    appName TEXT,
                    cost REAL DEFAULT 0,
                    platform TEXT,
                    status TEXT DEFAULT 'active',
                    onlineVersion TEXT DEFAULT '',
                    start_date TEXT NOT NULL,
                    end_date TEXT NOT NULL,
                    created_at TEXT NOT NULL,
                    updated_at TEXT NOT NULL,
                    UNIQUE(uid, appId, start_date, end_date)
                )
                ''')
                
                # 创建小游戏数据表
                await conn.execute(f'''
                CREATE TABLE IF NOT EXISTS {self.games_table} (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    appId TEXT NOT NULL,
                    appName TEXT,
                    todayCost REAL DEFAULT 0,
                    uids TEXT,
                    onlineVersion TEXT,
                    play_count INTEGER DEFAULT 0,
                    conversion_rate REAL DEFAULT 0.0,
                    revenue REAL DEFAULT 0.0,
                    start_date TEXT NOT NULL,
                    end_date TEXT NOT NULL,
                    created_at TEXT NOT NULL,
                    updated_at TEXT NOT NULL,
                    UNIQUE(appId, start_date, end_date)
                )
                ''')
                
                # 创建筛选配置表
                await conn.execute(f'''
                CREATE TABLE IF NOT EXISTS {self.configs_table} (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    group_name TEXT UNIQUE NOT NULL,
                    filter_version TEXT DEFAULT '[]',
                    filter_name TEXT DEFAULT '[]',
                    min_cost REAL DEFAULT 0,
                    min_MonetizationRoi REAL DEFAULT 0,
                    appId TEXT DEFAULT '',
                    uids TEXT DEFAULT '[]',
                    enabled BOOLEAN DEFAULT 1,
                    created_at TEXT NOT NULL,
                    updated_at TEXT NOT NULL
                )
                ''')

                # 检查并添加uids字段（为了兼容已存在的数据库）
                try:
                    await conn.execute(f'ALTER TABLE {self.configs_table} ADD COLUMN uids TEXT DEFAULT "[]"')
                except Exception:
                    # 字段已存在，忽略错误
                    pass

                # 检查并添加labelName字段（为了兼容已存在的数据库）
                try:
                    await conn.execute(f'ALTER TABLE {self.configs_table} ADD COLUMN labelName TEXT DEFAULT "[]"')
                except Exception:
                    # 字段已存在，忽略错误
                    pass

                # 检查并添加onlineVersion字段到投放账户表（为了兼容已存在的数据库）
                try:
                    await conn.execute(f'ALTER TABLE {self.accounts_table} ADD COLUMN onlineVersion TEXT DEFAULT ""')
                except Exception:
                    # 字段已存在，忽略错误
                    pass

                # 创建任务表
                await conn.execute('''
                CREATE TABLE IF NOT EXISTS tasks (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT UNIQUE NOT NULL,
                    description TEXT,
                    script_path TEXT NOT NULL,
                    task_type TEXT NOT NULL,
                    cron_expression TEXT,
                    interval_seconds INTEGER,
                    run_date TEXT,
                    enabled BOOLEAN DEFAULT 1,
                    max_instances INTEGER DEFAULT 1,
                    timeout INTEGER,
                    env_vars TEXT DEFAULT '{}',
                    status TEXT DEFAULT 'pending',
                    last_run_time TEXT,
                    next_run_time TEXT,
                    run_count INTEGER DEFAULT 0,
                    success_count INTEGER DEFAULT 0,
                    fail_count INTEGER DEFAULT 0,
                    created_at TEXT NOT NULL,
                    updated_at TEXT NOT NULL
                )
                ''')

                # 创建任务日志表
                await conn.execute('''
                CREATE TABLE IF NOT EXISTS task_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    task_id INTEGER NOT NULL,
                    status TEXT NOT NULL,
                    start_time TEXT NOT NULL,
                    end_time TEXT,
                    output TEXT,
                    error TEXT,
                    exit_code INTEGER,
                    duration REAL,
                    FOREIGN KEY (task_id) REFERENCES tasks (id) ON DELETE CASCADE
                )
                ''')



                # 创建任务视图表
                await conn.execute('''
                CREATE TABLE IF NOT EXISTS task_views (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL UNIQUE,
                    description TEXT,
                    filter_type TEXT NOT NULL DEFAULT 'path',
                    filter_value TEXT,
                    is_default BOOLEAN DEFAULT FALSE,
                    created_at TEXT NOT NULL,
                    updated_at TEXT NOT NULL
                )
                ''')

                # 插入默认的"全部任务"视图
                await conn.execute('''
                INSERT OR IGNORE INTO task_views (name, description, filter_type, filter_value, is_default, created_at, updated_at)
                VALUES ('全部任务', '显示所有任务', 'all', '', TRUE, datetime('now'), datetime('now'))
                ''')



                # 创建索引
                await conn.execute(f'CREATE INDEX IF NOT EXISTS idx_{self.accounts_table}_uid ON {self.accounts_table}(uid)')
                await conn.execute(f'CREATE INDEX IF NOT EXISTS idx_{self.accounts_table}_dates ON {self.accounts_table}(start_date, end_date)')
                await conn.execute(f'CREATE INDEX IF NOT EXISTS idx_{self.games_table}_appId ON {self.games_table}(appId)')
                await conn.execute(f'CREATE INDEX IF NOT EXISTS idx_{self.games_table}_dates ON {self.games_table}(start_date, end_date)')

                await conn.commit()
                
            logger.info(f"投放数据库初始化完成: {self.db_path}")
            
        except Exception as e:
            logger.error(f"投放数据库初始化失败: {e}")
            raise
    
    async def get_game_versions(self) -> Dict[str, str]:
        """获取小游戏版本信息映射"""
        game_versions = {}
        try:
            async with aiosqlite.connect(self.db_path) as conn:
                # 查询最新的小游戏版本信息
                cursor = await conn.execute(
                    f"SELECT appId, onlineVersion FROM {self.games_table} WHERE onlineVersion != '' ORDER BY updated_at DESC"
                )
                games = await cursor.fetchall()

                # 创建appId到onlineVersion的映射
                for game in games:
                    appId = game[0]
                    version = game[1]
                    if appId and version and appId not in game_versions:
                        game_versions[appId] = version

                logger.info(f"获取到{len(game_versions)}个小游戏版本信息")
        except Exception as e:
            logger.error(f"获取小游戏版本信息失败: {e}")

        return game_versions

    async def save_accounts(self, accounts: List[Dict], start_date: str, end_date: str,
                           clear_old_data: bool = True) -> int:
        """保存投放账户数据

        Args:
            accounts: 账户数据列表
            start_date: 开始日期
            end_date: 结束日期
            clear_old_data: 是否清空旧数据，默认True
        """
        if not accounts:
            return 0

        try:
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            saved_count = 0

            # 获取小游戏版本信息
            game_versions = await self.get_game_versions()

            async with aiosqlite.connect(self.db_path) as conn:
                # 如果需要清空旧数据，完全清空投放账户表
                if clear_old_data:
                    delete_cursor = await conn.execute(f"DELETE FROM {self.accounts_table}")
                    deleted_count = delete_cursor.rowcount
                    logger.info(f"清空旧数据: 完全清空投放账户表，删除了{deleted_count}条记录")
                    await conn.commit()

                # 保存投放账户数据
                for account in accounts:
                    uid = account.get('uid')
                    appId = account.get('appId')
                    appName = account.get('appName', '')
                    cost = account.get('cost', 0)
                    platform = account.get('platform', 'unknown')
                    status = account.get('status', 'active')

                    # 从小游戏版本映射中获取onlineVersion
                    onlineVersion = game_versions.get(appId or '', '')

                    if not uid or not appId:
                        continue
                    
                    # 检查是否已存在
                    cursor = await conn.execute(
                        f"SELECT id FROM {self.accounts_table} WHERE uid = ? AND appId = ? AND start_date = ? AND end_date = ?",
                        (uid, appId, start_date, end_date)
                    )
                    existing = await cursor.fetchone()
                    
                    if existing:
                        # 更新现有记录
                        await conn.execute(f'''
                        UPDATE {self.accounts_table}
                        SET appName = ?, cost = ?, platform = ?, status = ?, onlineVersion = ?, updated_at = ?
                        WHERE uid = ? AND appId = ? AND start_date = ? AND end_date = ?
                        ''', (appName, cost, platform, status, onlineVersion, current_time, uid, appId, start_date, end_date))
                    else:
                        # 插入新记录
                        await conn.execute(f'''
                        INSERT INTO {self.accounts_table}
                        (uid, appId, appName, cost, platform, status, onlineVersion, start_date, end_date, created_at, updated_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        ''', (uid, appId, appName, cost, platform, status, onlineVersion, start_date, end_date, current_time, current_time))
                    
                    saved_count += 1
                
                await conn.commit()
            
            logger.info(f"保存投放账户数据完成: {saved_count}条")
            return saved_count
            
        except Exception as e:
            logger.error(f"保存投放账户数据失败: {e}")
            raise
    
    async def save_games(self, games: List[Dict], start_date: str, end_date: str,
                        clear_old_data: bool = True) -> int:
        """保存小游戏数据

        Args:
            games: 小游戏数据列表
            start_date: 开始日期
            end_date: 结束日期
            clear_old_data: 是否清空旧数据，默认True
        """
        if not games:
            return 0

        try:
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            saved_count = 0

            async with aiosqlite.connect(self.db_path) as conn:
                # 如果需要清空旧数据，完全清空小游戏表
                if clear_old_data:
                    delete_cursor = await conn.execute(f"DELETE FROM {self.games_table}")
                    deleted_count = delete_cursor.rowcount
                    logger.info(f"清空旧数据: 完全清空小游戏表，删除了{deleted_count}条记录")
                    await conn.commit()
                for game in games:
                    appId = game.get('appId')
                    appName = game.get('appName', '')
                    todayCost = game.get('todayCost', 0)
                    uids = str(game.get('uids', []))
                    onlineVersion = game.get('onlineVersion', '')
                    
                    if not appId:
                        continue
                    
                    # 检查是否已存在
                    cursor = await conn.execute(
                        f"SELECT id FROM {self.games_table} WHERE appId = ? AND start_date = ? AND end_date = ?",
                        (appId, start_date, end_date)
                    )
                    existing = await cursor.fetchone()
                    
                    if existing:
                        # 更新现有记录
                        await conn.execute(f'''
                        UPDATE {self.games_table} 
                        SET appName = ?, todayCost = ?, uids = ?, onlineVersion = ?, updated_at = ?
                        WHERE appId = ? AND start_date = ? AND end_date = ?
                        ''', (appName, todayCost, uids, onlineVersion, current_time, appId, start_date, end_date))
                    else:
                        # 插入新记录
                        await conn.execute(f'''
                        INSERT INTO {self.games_table} 
                        (appId, appName, todayCost, uids, onlineVersion, start_date, end_date, created_at, updated_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                        ''', (appId, appName, todayCost, uids, onlineVersion, start_date, end_date, current_time, current_time))
                    
                    saved_count += 1
                
                await conn.commit()
            
            logger.info(f"保存小游戏数据完成: {saved_count}条")
            return saved_count
            
        except Exception as e:
            logger.error(f"保存小游戏数据失败: {e}")
            raise
    
    async def get_accounts(self, page: int = 1, limit: int = 20, search: Optional[str] = None,
                          name_search: Optional[str] = None, version_search: Optional[str] = None,
                          platform: Optional[str] = None, status: Optional[str] = None,
                          min_cost: Optional[float] = None) -> Dict[str, Any]:
        """获取投放账户列表

        Args:
            page: 页码
            limit: 每页数量
            search: 通用搜索（兼容旧版本）
            name_search: 应用名称搜索
            version_search: 版本搜索
            platform: 平台筛选
            status: 状态筛选
            min_cost: 最低花费筛选
        """
        try:
            offset = (page - 1) * limit
            conditions = []
            params = []

            # 通用搜索（兼容旧版本）
            if search:
                conditions.append("(appName LIKE ? OR uid LIKE ? OR appId LIKE ?)")
                params.extend([f"%{search}%", f"%{search}%", f"%{search}%"])

            # 应用名称搜索
            if name_search:
                conditions.append("(appName LIKE ? OR appId LIKE ?)")
                params.extend([f"%{name_search}%", f"%{name_search}%"])

            # 版本搜索
            if version_search:
                conditions.append("onlineVersion LIKE ?")
                params.append(f"%{version_search}%")

            # 最低花费筛选
            if min_cost is not None:
                conditions.append("(cost IS NOT NULL AND cost >= ?)")
                params.append(min_cost)

            if platform:
                conditions.append("platform = ?")
                params.append(platform)

            if status:
                conditions.append("status = ?")
                params.append(status)
            
            where_clause = " WHERE " + " AND ".join(conditions) if conditions else ""
            
            async with aiosqlite.connect(self.db_path) as conn:
                conn.row_factory = aiosqlite.Row
                
                # 获取总数
                count_sql = f"SELECT COUNT(*) FROM {self.accounts_table}{where_clause}"
                cursor = await conn.execute(count_sql, params)
                result = await cursor.fetchone()
                total = result[0] if result else 0
                
                # 获取数据
                data_sql = f"""
                SELECT * FROM {self.accounts_table}{where_clause} 
                ORDER BY updated_at DESC LIMIT ? OFFSET ?
                """
                cursor = await conn.execute(data_sql, params + [limit, offset])
                rows = await cursor.fetchall()
                
                items = [dict(row) for row in rows]
                
                return {
                    "total": total,
                    "items": items,
                    "page": page,
                    "limit": limit
                }
                
        except Exception as e:
            logger.error(f"获取投放账户列表失败: {e}")
            raise
    
    async def get_games(self, page: int = 1, limit: int = 20, search: Optional[str] = None) -> Dict[str, Any]:
        """获取小游戏数据列表"""
        try:
            offset = (page - 1) * limit
            conditions = []
            params = []
            
            if search:
                conditions.append("(appName LIKE ? OR appId LIKE ?)")
                params.extend([f"%{search}%", f"%{search}%"])
            
            where_clause = " WHERE " + " AND ".join(conditions) if conditions else ""
            
            async with aiosqlite.connect(self.db_path) as conn:
                conn.row_factory = aiosqlite.Row
                
                # 获取总数
                count_sql = f"SELECT COUNT(*) FROM {self.games_table}{where_clause}"
                cursor = await conn.execute(count_sql, params)
                result = await cursor.fetchone()
                total = result[0] if result else 0
                
                # 获取数据
                data_sql = f"""
                SELECT * FROM {self.games_table}{where_clause} 
                ORDER BY todayCost DESC LIMIT ? OFFSET ?
                """
                cursor = await conn.execute(data_sql, params + [limit, offset])
                rows = await cursor.fetchall()
                
                items = [dict(row) for row in rows]
                
                return {
                    "total": total,
                    "items": items,
                    "page": page,
                    "limit": limit
                }
                
        except Exception as e:
            logger.error(f"获取小游戏数据列表失败: {e}")
            raise
    
    async def get_stats(self) -> Dict[str, Any]:
        """获取统计数据"""
        try:
            async with aiosqlite.connect(self.db_path) as conn:
                # 投放账户统计
                cursor = await conn.execute(f"SELECT COUNT(*) FROM {self.accounts_table}")
                result = await cursor.fetchone()
                total_accounts = result[0] if result else 0

                cursor = await conn.execute(f"SELECT SUM(cost) FROM {self.accounts_table}")
                result = await cursor.fetchone()
                total_cost = (result[0] if result and result[0] is not None else 0)

                # 小游戏统计
                cursor = await conn.execute(f"SELECT COUNT(*) FROM {self.games_table}")
                result = await cursor.fetchone()
                total_games = result[0] if result else 0

                cursor = await conn.execute(f"SELECT SUM(todayCost) FROM {self.games_table}")
                result = await cursor.fetchone()
                total_game_cost = (result[0] if result and result[0] is not None else 0)
                
                return {
                    "total_accounts": total_accounts,
                    "total_cost": round(total_cost, 2),
                    "total_games": total_games,
                    "total_game_cost": round(total_game_cost, 2)
                }
                
        except Exception as e:
            logger.error(f"获取统计数据失败: {e}")
            raise

    # ==================== 配置管理相关方法 ====================

    async def get_filter_configs(self, page: int = 1, limit: int = 20, search: Optional[str] = None, get_all: bool = False) -> Dict[str, Any]:
        """获取筛选配置列表"""
        try:
            async with aiosqlite.connect(self.db_path) as conn:
                # 构建查询条件
                where_conditions = []
                params = []

                if search:
                    # 支持在配置组名称、筛选版本、筛选名称、应用ID中搜索
                    # 由于filter_version和filter_name是JSON格式，使用LIKE搜索JSON内容
                    where_conditions.append("""
                        (group_name LIKE ? OR
                         filter_version LIKE ? OR
                         filter_name LIKE ? OR
                         appId LIKE ?)
                    """)
                    search_pattern = f"%{search}%"
                    params.extend([search_pattern, search_pattern, search_pattern, search_pattern])

                where_clause = " WHERE " + " AND ".join(where_conditions) if where_conditions else ""

                # 获取总数
                count_sql = f"SELECT COUNT(*) FROM {self.configs_table}{where_clause}"
                cursor = await conn.execute(count_sql, params)
                result = await cursor.fetchone()
                total = result[0] if result else 0

                # 获取数据
                if get_all:
                    # 获取全部数据，不分页
                    data_sql = f"""
                    SELECT id, group_name, filter_version, filter_name, min_cost,
                           min_MonetizationRoi, appId, labelName, uids, enabled, created_at, updated_at
                    FROM {self.configs_table}{where_clause}
                    ORDER BY created_at DESC
                    """
                    cursor = await conn.execute(data_sql, params)
                else:
                    # 分页查询
                    offset = (page - 1) * limit
                    data_sql = f"""
                    SELECT id, group_name, filter_version, filter_name, min_cost,
                           min_MonetizationRoi, appId, labelName, uids, enabled, created_at, updated_at
                    FROM {self.configs_table}{where_clause}
                    ORDER BY created_at DESC
                    LIMIT ? OFFSET ?
                    """
                    cursor = await conn.execute(data_sql, params + [limit, offset])
                rows = await cursor.fetchall()

                items = []
                for row in rows:
                    item = {
                        'id': row[0],
                        'group_name': row[1],
                        'filter_version': safe_json_loads(row[2]),
                        'filter_name': safe_json_loads(row[3]),
                        'min_cost': row[4],
                        'min_MonetizationRoi': row[5],
                        'appId': row[6],
                        'labelName': safe_json_loads(row[7]),
                        'uids': safe_json_loads(row[8]),
                        'enabled': bool(row[9]),
                        'created_at': row[10],
                        'updated_at': row[11]
                    }
                    items.append(item)

                return {
                    'total': total,
                    'items': items,
                    'page': page,
                    'limit': limit
                }

        except Exception as e:
            logger.error(f"获取筛选配置失败: {e}")
            raise

    async def get_all_filter_configs(self, search: Optional[str] = None) -> List[Dict[str, Any]]:
        """获取全部筛选配置（不分页）"""
        try:
            async with aiosqlite.connect(self.db_path) as conn:
                # 构建查询条件
                where_conditions = []
                params = []

                if search:
                    where_conditions.append("group_name LIKE ?")
                    params.append(f"%{search}%")

                where_clause = " WHERE " + " AND ".join(where_conditions) if where_conditions else ""

                # 获取全部数据
                data_sql = f"""
                SELECT id, group_name, filter_version, filter_name, min_cost,
                       min_MonetizationRoi, appId, labelName, uids, enabled, created_at, updated_at
                FROM {self.configs_table}{where_clause}
                ORDER BY created_at DESC
                """

                cursor = await conn.execute(data_sql, params)
                rows = await cursor.fetchall()

                items = []
                for row in rows:
                    item = {
                        'id': row[0],
                        'group_name': row[1],
                        'filter_version': safe_json_loads(row[2]),
                        'filter_name': safe_json_loads(row[3]),
                        'min_cost': row[4],
                        'min_MonetizationRoi': row[5],
                        'appId': row[6],
                        'labelName': safe_json_loads(row[7]),
                        'uids': safe_json_loads(row[8]),
                        'enabled': bool(row[9]),
                        'created_at': row[10],
                        'updated_at': row[11]
                    }
                    items.append(item)

                return items

        except Exception as e:
            logger.error(f"获取全部筛选配置失败: {e}")
            raise

    async def get_config_by_appid(self, app_id: str) -> Optional[Dict[str, Any]]:
        """根据appId查询配置"""
        try:
            async with aiosqlite.connect(self.db_path) as conn:
                sql = f"""
                SELECT id, group_name, filter_version, filter_name, min_cost,
                       min_MonetizationRoi, appId, labelName, uids, enabled, created_at, updated_at
                FROM {self.configs_table}
                WHERE appId = ?
                """

                cursor = await conn.execute(sql, (app_id,))
                row = await cursor.fetchone()

                if row:
                    return {
                        'id': row[0],
                        'group_name': row[1],
                        'filter_version': safe_json_loads(row[2]),
                        'filter_name': safe_json_loads(row[3]),
                        'min_cost': row[4],
                        'min_MonetizationRoi': row[5],
                        'appId': row[6],
                        'labelName': safe_json_loads(row[7]),
                        'uids': safe_json_loads(row[8]),
                        'enabled': bool(row[9]),
                        'created_at': row[10],
                        'updated_at': row[11]
                    }

                return None

        except Exception as e:
            logger.error(f"根据appId查询配置失败: {e}")
            raise

    async def create_filter_config(self, config_data: Dict[str, Any]) -> bool:
        """创建筛选配置"""
        try:
            async with aiosqlite.connect(self.db_path) as conn:
                current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

                # 生成配置组名称（如果未提供）
                group_name = config_data.get('group_name')
                if not group_name:
                    # 获取当前最大ID
                    cursor = await conn.execute(f"SELECT MAX(id) FROM {self.configs_table}")
                    result = await cursor.fetchone()
                    max_id = result[0] if result and result[0] is not None else 0
                    group_name = f"config_{max_id + 1}"

                # 将列表转换为JSON字符串
                filter_version = json.dumps(config_data.get('filter_version', []), ensure_ascii=False)
                filter_name = json.dumps(config_data.get('filter_name', []), ensure_ascii=False)
                label_name = json.dumps(config_data.get('labelName', []), ensure_ascii=False)
                uids = json.dumps(config_data.get('uids', []), ensure_ascii=False)

                await conn.execute(f'''
                INSERT INTO {self.configs_table}
                (group_name, filter_version, filter_name, min_cost, min_MonetizationRoi,
                 appId, labelName, uids, enabled, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    group_name,
                    filter_version,
                    filter_name,
                    config_data.get('min_cost', 0),
                    config_data.get('min_MonetizationRoi', 0),
                    config_data.get('appId', ''),
                    label_name,
                    uids,
                    config_data.get('enabled', True),
                    current_time,
                    current_time
                ))

                await conn.commit()
                logger.info(f"创建筛选配置成功: {group_name}")
                return True

        except Exception as e:
            logger.error(f"创建筛选配置失败: {e}")
            raise

    async def update_filter_config(self, config_id: int, config_data: Dict[str, Any]) -> bool:
        """更新筛选配置"""
        try:
            async with aiosqlite.connect(self.db_path) as conn:
                current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

                # 将列表转换为JSON字符串
                filter_version = json.dumps(config_data.get('filter_version', []), ensure_ascii=False)
                filter_name = json.dumps(config_data.get('filter_name', []), ensure_ascii=False)
                label_name = json.dumps(config_data.get('labelName', []), ensure_ascii=False)
                uids = json.dumps(config_data.get('uids', []), ensure_ascii=False)

                await conn.execute(f'''
                UPDATE {self.configs_table}
                SET filter_version = ?, filter_name = ?, min_cost = ?, min_MonetizationRoi = ?,
                    appId = ?, labelName = ?, uids = ?, enabled = ?, updated_at = ?
                WHERE id = ?
                ''', (
                    filter_version,
                    filter_name,
                    config_data.get('min_cost', 0),
                    config_data.get('min_MonetizationRoi', 0),
                    config_data.get('appId', ''),
                    label_name,
                    uids,
                    config_data.get('enabled', True),
                    current_time,
                    config_id
                ))

                await conn.commit()
                logger.info(f"更新筛选配置成功: ID {config_id}")
                return True

        except Exception as e:
            logger.error(f"更新筛选配置失败: {e}")
            raise

    async def delete_filter_config(self, config_id: int) -> bool:
        """删除筛选配置"""
        try:
            async with aiosqlite.connect(self.db_path) as conn:
                await conn.execute(f"DELETE FROM {self.configs_table} WHERE id = ?", (config_id,))
                await conn.commit()
                logger.info(f"删除筛选配置成功: ID {config_id}")
                return True

        except Exception as e:
            logger.error(f"删除筛选配置失败: {e}")
            raise

    # ==================== 任务管理相关方法 ====================

    async def create_task(self, task_data: Dict[str, Any]) -> bool:
        """创建任务"""
        try:
            async with aiosqlite.connect(self.db_path) as conn:
                current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

                await conn.execute('''
                INSERT INTO tasks
                (name, description, script_path, task_type, cron_expression,
                 interval_seconds, run_date, enabled, max_instances, timeout,
                 env_vars, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    task_data.get('name'),
                    task_data.get('description'),
                    task_data.get('script_path'),
                    task_data.get('task_type'),
                    task_data.get('cron_expression'),
                    task_data.get('interval_seconds'),
                    task_data.get('run_date'),
                    task_data.get('enabled', True),
                    task_data.get('max_instances', 1),
                    task_data.get('timeout'),
                    json.dumps(task_data.get('env_vars', {}), ensure_ascii=False),
                    current_time,
                    current_time
                ))

                await conn.commit()
                logger.info(f"创建任务成功: {task_data.get('name')}")
                return True

        except Exception as e:
            logger.error(f"创建任务失败: {e}")
            raise

    async def get_tasks(self, page: int = 1, limit: int = 20, search: Optional[str] = None) -> Dict[str, Any]:
        """获取任务列表"""
        try:
            async with aiosqlite.connect(self.db_path) as conn:
                # 构建查询条件
                where_conditions = []
                params = []

                if search:
                    where_conditions.append("(name LIKE ? OR description LIKE ?)")
                    params.extend([f"%{search}%", f"%{search}%"])

                where_clause = " WHERE " + " AND ".join(where_conditions) if where_conditions else ""

                # 获取总数
                count_sql = f"SELECT COUNT(*) FROM tasks{where_clause}"
                cursor = await conn.execute(count_sql, params)
                result = await cursor.fetchone()
                total = result[0] if result else 0

                # 获取数据
                offset = (page - 1) * limit
                data_sql = f"""
                SELECT t.id, t.name, t.description, t.script_path, t.task_type, t.cron_expression,
                       t.interval_seconds, t.run_date, t.enabled, t.max_instances, t.timeout,
                       t.env_vars, t.status, t.last_run_time, t.next_run_time, t.run_count,
                       t.success_count, t.fail_count, t.created_at, t.updated_at,
                       COALESCE(ROUND(latest_log.duration), 0) as last_run_duration
                FROM tasks t
                LEFT JOIN (
                    SELECT task_id, duration,
                           ROW_NUMBER() OVER (PARTITION BY task_id ORDER BY start_time DESC) as rn
                    FROM task_logs
                    WHERE end_time IS NOT NULL
                ) latest_log ON t.id = latest_log.task_id AND latest_log.rn = 1
                {where_clause.replace('name', 't.name').replace('script_path', 't.script_path') if where_clause else ''}
                ORDER BY t.created_at DESC
                LIMIT ? OFFSET ?
                """

                cursor = await conn.execute(data_sql, params + [limit, offset])
                rows = await cursor.fetchall()

                # 转换数据
                items = []
                for row in rows:
                    item = {
                        'id': row[0],
                        'name': row[1],
                        'description': row[2],
                        'script_path': row[3],
                        'task_type': row[4],
                        'cron_expression': row[5],
                        'interval_seconds': row[6],
                        'run_date': row[7],
                        'enabled': bool(row[8]),
                        'max_instances': row[9],
                        'timeout': row[10],
                        'env_vars': json.loads(row[11]) if row[11] else {},
                        'status': row[12],
                        'last_run_time': row[13],
                        'next_run_time': row[14],
                        'run_count': row[15],
                        'success_count': row[16],
                        'fail_count': row[17],
                        'created_at': row[18],
                        'updated_at': row[19],
                        'last_run_duration': row[20] if row[20] else None
                    }
                    items.append(item)

                return {
                    'items': items,
                    'total': total,
                    'page': page,
                    'limit': limit,
                    'pages': (total + limit - 1) // limit
                }

        except Exception as e:
            logger.error(f"获取任务列表失败: {e}")
            raise

    async def get_task_by_id(self, task_id: int) -> Optional[Dict[str, Any]]:
        """根据ID获取任务"""
        try:
            async with aiosqlite.connect(self.db_path) as conn:
                cursor = await conn.execute('''
                SELECT id, name, description, script_path, task_type, cron_expression,
                       interval_seconds, run_date, enabled, max_instances, timeout,
                       env_vars, status, last_run_time, next_run_time, run_count,
                       success_count, fail_count, created_at, updated_at
                FROM tasks WHERE id = ?
                ''', (task_id,))

                row = await cursor.fetchone()
                if row:
                    return {
                        'id': row[0],
                        'name': row[1],
                        'description': row[2],
                        'script_path': row[3],
                        'task_type': row[4],
                        'cron_expression': row[5],
                        'interval_seconds': row[6],
                        'run_date': row[7],
                        'enabled': bool(row[8]),
                        'max_instances': row[9],
                        'timeout': row[10],
                        'env_vars': json.loads(row[11]) if row[11] else {},
                        'status': row[12],
                        'last_run_time': row[13],
                        'next_run_time': row[14],
                        'run_count': row[15],
                        'success_count': row[16],
                        'fail_count': row[17],
                        'created_at': row[18],
                        'updated_at': row[19]
                    }
                return None

        except Exception as e:
            logger.error(f"获取任务失败: {e}")
            raise

    async def update_task(self, task_id: int, task_data: Dict[str, Any]) -> bool:
        """更新任务"""
        try:
            async with aiosqlite.connect(self.db_path) as conn:
                current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

                await conn.execute('''
                UPDATE tasks SET
                name = ?, description = ?, script_path = ?, task_type = ?,
                cron_expression = ?, interval_seconds = ?, run_date = ?,
                enabled = ?, max_instances = ?, timeout = ?, env_vars = ?,
                updated_at = ?
                WHERE id = ?
                ''', (
                    task_data.get('name'),
                    task_data.get('description'),
                    task_data.get('script_path'),
                    task_data.get('task_type'),
                    task_data.get('cron_expression'),
                    task_data.get('interval_seconds'),
                    task_data.get('run_date'),
                    task_data.get('enabled', True),
                    task_data.get('max_instances', 1),
                    task_data.get('timeout'),
                    json.dumps(task_data.get('env_vars', {}), ensure_ascii=False),
                    current_time,
                    task_id
                ))

                await conn.commit()
                logger.info(f"更新任务成功: ID {task_id}")
                return True

        except Exception as e:
            logger.error(f"更新任务失败: {e}")
            raise

    async def delete_task(self, task_id: int) -> bool:
        """删除任务"""
        try:
            async with aiosqlite.connect(self.db_path) as conn:
                await conn.execute('DELETE FROM tasks WHERE id = ?', (task_id,))
                await conn.commit()
                logger.info(f"删除任务成功: ID {task_id}")
                return True

        except Exception as e:
            logger.error(f"删除任务失败: {e}")
            raise

    async def update_task_status(self, task_id: int, status: str, run_time: datetime):
        """更新任务状态"""
        try:
            async with aiosqlite.connect(self.db_path) as conn:
                current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                run_time_str = run_time.strftime('%Y-%m-%d %H:%M:%S')

                await conn.execute('''
                UPDATE tasks SET status = ?, last_run_time = ?, updated_at = ?
                WHERE id = ?
                ''', (status, run_time_str, current_time, task_id))

                # 增加运行次数
                await conn.execute('''
                UPDATE tasks SET run_count = run_count + 1 WHERE id = ?
                ''', (task_id,))

                await conn.commit()

        except Exception as e:
            logger.error(f"更新任务状态失败: {e}")
            raise

    async def increment_task_success_count(self, task_id: int):
        """增加任务成功次数"""
        try:
            async with aiosqlite.connect(self.db_path) as conn:
                await conn.execute('''
                UPDATE tasks SET success_count = success_count + 1 WHERE id = ?
                ''', (task_id,))
                await conn.commit()
        except Exception as e:
            logger.error(f"增加任务成功次数失败: {e}")
            raise

    async def increment_task_fail_count(self, task_id: int):
        """增加任务失败次数"""
        try:
            async with aiosqlite.connect(self.db_path) as conn:
                await conn.execute('''
                UPDATE tasks SET fail_count = fail_count + 1 WHERE id = ?
                ''', (task_id,))
                await conn.commit()
        except Exception as e:
            logger.error(f"增加任务失败次数失败: {e}")
            raise

    async def update_task_next_run_time(self, task_id: int, next_run_time: str):
        """更新任务下次运行时间"""
        try:
            async with aiosqlite.connect(self.db_path) as conn:
                current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                await conn.execute('''
                UPDATE tasks SET next_run_time = ?, updated_at = ? WHERE id = ?
                ''', (next_run_time, current_time, task_id))
                await conn.commit()
        except Exception as e:
            logger.error(f"更新任务下次运行时间失败: {e}")
            raise

    async def get_enabled_tasks(self) -> List[Dict[str, Any]]:
        """获取启用的任务"""
        try:
            async with aiosqlite.connect(self.db_path) as conn:
                cursor = await conn.execute('''
                SELECT id, name, description, script_path, task_type, cron_expression,
                       interval_seconds, run_date, enabled, max_instances, timeout,
                       env_vars, status, last_run_time, next_run_time, run_count,
                       success_count, fail_count, created_at, updated_at
                FROM tasks WHERE enabled = 1
                ''')

                rows = await cursor.fetchall()
                tasks = []
                for row in rows:
                    task = {
                        'id': row[0],
                        'name': row[1],
                        'description': row[2],
                        'script_path': row[3],
                        'task_type': row[4],
                        'cron_expression': row[5],
                        'interval_seconds': row[6],
                        'run_date': row[7],
                        'enabled': bool(row[8]),
                        'max_instances': row[9],
                        'timeout': row[10],
                        'env_vars': json.loads(row[11]) if row[11] else {},
                        'status': row[12],
                        'last_run_time': row[13],
                        'next_run_time': row[14],
                        'run_count': row[15],
                        'success_count': row[16],
                        'fail_count': row[17],
                        'created_at': row[18],
                        'updated_at': row[19]
                    }
                    tasks.append(task)

                return tasks

        except Exception as e:
            logger.error(f"获取启用任务失败: {e}")
            raise

    async def create_task_log(self, task_id: int, status: str, start_time: datetime) -> int:
        """创建任务日志"""
        try:
            async with aiosqlite.connect(self.db_path) as conn:
                start_time_str = start_time.strftime('%Y-%m-%d %H:%M:%S')

                cursor = await conn.execute('''
                INSERT INTO task_logs (task_id, status, start_time)
                VALUES (?, ?, ?)
                ''', (task_id, status, start_time_str))

                await conn.commit()
                return cursor.lastrowid or 0

        except Exception as e:
            logger.error(f"创建任务日志失败: {e}")
            raise

    async def update_task_log(self, log_id: int, status: str, end_time: datetime,
                             output: str, error: str, exit_code: int):
        """更新任务日志"""
        try:
            async with aiosqlite.connect(self.db_path) as conn:
                end_time_str = end_time.strftime('%Y-%m-%d %H:%M:%S')

                # 计算执行时长
                cursor = await conn.execute('SELECT start_time FROM task_logs WHERE id = ?', (log_id,))
                row = await cursor.fetchone()
                if row:
                    start_time = datetime.strptime(row[0], '%Y-%m-%d %H:%M:%S')
                    duration = (end_time - start_time).total_seconds()
                else:
                    duration = 0

                await conn.execute('''
                UPDATE task_logs SET
                status = ?, end_time = ?, output = ?, error = ?,
                exit_code = ?, duration = ?
                WHERE id = ?
                ''', (status, end_time_str, output, error, exit_code, duration, log_id))

                await conn.commit()

        except Exception as e:
            logger.error(f"更新任务日志失败: {e}")
            raise

    async def update_task_log_output(self, log_id: int, output: str, error: str):
        """实时更新任务日志的输出内容（不更新状态和结束时间）"""
        try:
            async with aiosqlite.connect(self.db_path) as conn:
                await conn.execute('''
                UPDATE task_logs SET output = ?, error = ?
                WHERE id = ?
                ''', (output, error, log_id))
                await conn.commit()
        except Exception as e:
            logger.error(f"实时更新任务日志输出失败: {e}")
            # 不抛出异常，避免影响任务执行

    async def get_task_logs(self, task_id: int, page: int = 1, limit: int = 20) -> Dict[str, Any]:
        """获取任务日志"""
        try:
            async with aiosqlite.connect(self.db_path) as conn:
                # 获取总数
                cursor = await conn.execute('SELECT COUNT(*) FROM task_logs WHERE task_id = ?', (task_id,))
                result = await cursor.fetchone()
                total = result[0] if result else 0

                # 获取数据
                offset = (page - 1) * limit
                cursor = await conn.execute('''
                SELECT id, task_id, status, start_time, end_time, output, error, exit_code, duration
                FROM task_logs WHERE task_id = ?
                ORDER BY start_time DESC
                LIMIT ? OFFSET ?
                ''', (task_id, limit, offset))

                rows = await cursor.fetchall()
                items = []
                for row in rows:
                    item = {
                        'id': row[0],
                        'task_id': row[1],
                        'status': row[2],
                        'start_time': row[3],
                        'end_time': row[4],
                        'output': row[5],
                        'error': row[6],
                        'exit_code': row[7],
                        'duration': round(row[8]) if row[8] is not None else None
                    }
                    items.append(item)

                return {
                    'items': items,
                    'total': total,
                    'page': page,
                    'limit': limit,
                    'pages': (total + limit - 1) // limit
                }

        except Exception as e:
            logger.error(f"获取任务日志失败: {e}")
            raise

    # ==================== 任务视图管理 ====================

    async def get_task_views(self) -> List[Dict[str, Any]]:
        """获取所有任务视图"""
        try:
            async with aiosqlite.connect(self.db_path) as conn:
                cursor = await conn.execute('''
                SELECT id, name, description, filter_type, filter_value, is_default, created_at, updated_at
                FROM task_views
                ORDER BY is_default DESC, name ASC
                ''')
                rows = await cursor.fetchall()

                views = []
                for row in rows:
                    view_id = row[0]
                    filter_type = row[3]
                    filter_value = row[4]

                    # 计算每个视图的任务数量
                    task_count = await self._get_view_task_count(conn, filter_type, filter_value)

                    views.append({
                        'id': view_id,
                        'name': row[1],
                        'description': row[2],
                        'filter_type': filter_type,
                        'filter_value': filter_value,
                        'is_default': bool(row[5]),
                        'created_at': row[6],
                        'updated_at': row[7],
                        'task_count': task_count
                    })

                return views
        except Exception as e:
            logger.error(f"获取任务视图失败: {e}")
            raise

    async def _get_view_task_count(self, conn, filter_type: str, filter_value: str) -> int:
        """获取视图中的任务数量"""
        try:
            # 构建查询条件
            where_clause = ""
            params = []

            if filter_type == 'path' and filter_value:
                where_clause = "WHERE script_path LIKE ?"
                params.append(f"%{filter_value}%")
            elif filter_type == 'name' and filter_value:
                where_clause = "WHERE name LIKE ?"
                params.append(f"%{filter_value}%")
            # filter_type == 'all' 时不添加条件，显示所有任务

            # 获取任务数量
            count_query = f"SELECT COUNT(*) FROM tasks {where_clause}"
            cursor = await conn.execute(count_query, params)
            result = await cursor.fetchone()
            return result[0] if result else 0
        except Exception as e:
            logger.error(f"获取视图任务数量失败: {e}")
            return 0

    async def create_task_view(self, name: str, description: str, filter_type: str, filter_value: str) -> int:
        """创建任务视图"""
        try:
            async with aiosqlite.connect(self.db_path) as conn:
                cursor = await conn.execute('''
                INSERT INTO task_views (name, description, filter_type, filter_value, is_default, created_at, updated_at)
                VALUES (?, ?, ?, ?, FALSE, datetime('now'), datetime('now'))
                ''', (name, description, filter_type, filter_value))

                await conn.commit()
                return cursor.lastrowid or 0
        except Exception as e:
            logger.error(f"创建任务视图失败: {e}")
            raise

    async def update_task_view(self, view_id: int, name: str, description: str, filter_type: str, filter_value: str):
        """更新任务视图"""
        try:
            async with aiosqlite.connect(self.db_path) as conn:
                await conn.execute('''
                UPDATE task_views
                SET name = ?, description = ?, filter_type = ?, filter_value = ?, updated_at = datetime('now')
                WHERE id = ?
                ''', (name, description, filter_type, filter_value, view_id))

                await conn.commit()
        except Exception as e:
            logger.error(f"更新任务视图失败: {e}")
            raise

    async def delete_task_view(self, view_id: int):
        """删除任务视图（不能删除默认视图）"""
        try:
            async with aiosqlite.connect(self.db_path) as conn:
                # 检查是否为默认视图
                cursor = await conn.execute('SELECT is_default FROM task_views WHERE id = ?', (view_id,))
                row = await cursor.fetchone()

                if row and row[0]:
                    raise ValueError("不能删除默认视图")

                await conn.execute('DELETE FROM task_views WHERE id = ?', (view_id,))
                await conn.commit()
        except Exception as e:
            logger.error(f"删除任务视图失败: {e}")
            raise

    async def get_tasks_by_view(self, view_id: int, page: int = 1, limit: int = 20) -> Dict[str, Any]:
        """根据视图获取任务列表"""
        try:
            async with aiosqlite.connect(self.db_path) as conn:
                # 获取视图信息
                cursor = await conn.execute('''
                SELECT filter_type, filter_value FROM task_views WHERE id = ?
                ''', (view_id,))
                view_row = await cursor.fetchone()

                if not view_row:
                    raise ValueError(f"视图不存在: {view_id}")

                filter_type, filter_value = view_row

                # 构建查询条件
                where_clause = ""
                params = []

                if filter_type == 'path' and filter_value:
                    where_clause = "WHERE script_path LIKE ?"
                    params.append(f"%{filter_value}%")
                elif filter_type == 'name' and filter_value:
                    where_clause = "WHERE name LIKE ?"
                    params.append(f"%{filter_value}%")
                # filter_type == 'all' 时不添加条件，显示所有任务

                # 获取总数
                count_query = f"SELECT COUNT(*) FROM tasks {where_clause}"
                cursor = await conn.execute(count_query, params)
                result = await cursor.fetchone()
                total = result[0] if result else 0

                # 获取任务列表
                offset = (page - 1) * limit
                query = f'''
                SELECT t.id, t.name, t.description, t.script_path, t.task_type, t.cron_expression,
                       t.interval_seconds, t.run_date, t.enabled, t.max_instances, t.timeout,
                       t.env_vars, t.created_at, t.updated_at, t.last_run_time, t.next_run_time,
                       t.success_count, t.fail_count, t.status,
                       COALESCE(ROUND(latest_log.duration), 0) as last_run_duration
                FROM tasks t
                LEFT JOIN (
                    SELECT task_id, duration,
                           ROW_NUMBER() OVER (PARTITION BY task_id ORDER BY start_time DESC) as rn
                    FROM task_logs
                    WHERE end_time IS NOT NULL
                ) latest_log ON t.id = latest_log.task_id AND latest_log.rn = 1
                {where_clause.replace('name', 't.name') if where_clause else ''}
                ORDER BY t.created_at DESC
                LIMIT ? OFFSET ?
                '''

                cursor = await conn.execute(query, params + [limit, offset])
                rows = await cursor.fetchall()

                tasks = []
                for row in rows:
                    env_vars = {}
                    if row[11]:  # env_vars
                        try:
                            env_vars = json.loads(row[11])
                        except:
                            pass

                    tasks.append({
                        'id': row[0],
                        'name': row[1],
                        'description': row[2],
                        'script_path': row[3],
                        'task_type': row[4],
                        'cron_expression': row[5],
                        'interval_seconds': row[6],
                        'run_date': row[7],
                        'enabled': bool(row[8]),
                        'max_instances': row[9],
                        'timeout': row[10],
                        'env_vars': env_vars,
                        'created_at': row[12],
                        'updated_at': row[13],
                        'last_run_time': row[14],
                        'next_run_time': row[15],
                        'success_count': row[16] or 0,
                        'fail_count': row[17] or 0,
                        'status': row[18] or 'pending',
                        'last_run_duration': row[19] if row[19] else None
                    })

                return {
                    'items': tasks,
                    'total': total,
                    'page': page,
                    'limit': limit,
                    'pages': (total + limit - 1) // limit
                }

        except Exception as e:
            logger.error(f"根据视图获取任务失败: {e}")
            raise






# 创建数据库实例
ad_database = AdDatabase()
