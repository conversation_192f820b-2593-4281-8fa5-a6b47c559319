#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import aiosqlite
import os
from passlib.context import CryptContext

# 密码加密上下文
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

class Database:
    """数据库操作类"""
    
    def __init__(self):
        self.database_path = "database/admin.db"
    
    async def get_connection(self):
        """获取数据库连接"""
        conn = await aiosqlite.connect(self.database_path)
        return conn
    
    async def init_database(self):
        """初始化数据库"""
        # 确保数据库目录存在
        os.makedirs('database', exist_ok=True)

        conn = await aiosqlite.connect(self.database_path)
        try:
            # 创建用户表
            await conn.execute('''
                CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT UNIQUE NOT NULL,
                    password TEXT NOT NULL,
                    email TEXT,
                    role TEXT DEFAULT 'user',
                    avatar TEXT,
                    introduction TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # 创建默认管理员账户
            admin_password = pwd_context.hash('123456')
            try:
                await conn.execute('''
                    INSERT INTO users (username, password, email, role, introduction)
                    VALUES (?, ?, ?, ?, ?)
                ''', ('admin', admin_password, '<EMAIL>', 'admin', '系统管理员'))
                await conn.commit()
                print("默认管理员账户创建成功: admin/123456")
            except aiosqlite.IntegrityError:
                print("管理员账户已存在")

            # 创建编辑员账户
            editor_password = pwd_context.hash('123456')
            try:
                await conn.execute('''
                    INSERT INTO users (username, password, email, role, introduction)
                    VALUES (?, ?, ?, ?, ?)
                ''', ('editor', editor_password, '<EMAIL>', 'editor', '内容编辑员'))
                await conn.commit()
                print("默认编辑员账户创建成功: editor/123456")
            except aiosqlite.IntegrityError:
                print("编辑员账户已存在")
        finally:
            await conn.close()
    
    async def get_user_by_username(self, username: str):
        """根据用户名获取用户"""
        conn = await aiosqlite.connect(self.database_path)
        try:
            conn.row_factory = aiosqlite.Row
            cursor = await conn.execute(
                'SELECT * FROM users WHERE username = ?', (username,)
            )
            return await cursor.fetchone()
        finally:
            await conn.close()

    async def get_user_by_id(self, user_id: int):
        """根据ID获取用户"""
        conn = await aiosqlite.connect(self.database_path)
        try:
            conn.row_factory = aiosqlite.Row
            cursor = await conn.execute(
                'SELECT id, username, email, role, avatar, introduction FROM users WHERE id = ?',
                (user_id,)
            )
            return await cursor.fetchone()
        finally:
            await conn.close()
    
    def verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """验证密码"""
        return pwd_context.verify(plain_password, hashed_password)
    
    def get_password_hash(self, password: str) -> str:
        """获取密码哈希"""
        return pwd_context.hash(password)

# 创建数据库实例
database = Database()
