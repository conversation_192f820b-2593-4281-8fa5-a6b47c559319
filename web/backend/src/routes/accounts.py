#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
投放账户API
"""

from fastapi import APIRouter, HTTPException, Depends
from typing import Dict, List, Optional
from ..utils.ad_database import AdDatabase
from ..models.response import ApiResponse
import logging

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/api/vue-element-admin", tags=["投放账户"])

@router.get("/accounts", response_model=ApiResponse)
async def get_accounts(
    page: int = 1,
    limit: int = 20,
    search: Optional[str] = None,
    name_search: Optional[str] = None,
    version_search: Optional[str] = None,
    platform: Optional[str] = None,
    status: Optional[str] = None,
    min_cost: Optional[float] = None,
    db: AdDatabase = Depends(lambda: AdDatabase())
):
    """获取投放账户列表

    Args:
        page: 页码
        limit: 每页数量
        search: 通用搜索（兼容旧版本）
        name_search: 应用名称搜索
        version_search: 版本搜索
        platform: 平台筛选
        status: 状态筛选
        min_cost: 最低花费筛选
    """
    try:
        result = await db.get_accounts(page, limit, search, name_search, version_search, platform, status, min_cost)
        return ApiResponse(code=20000, message="获取成功", data=result)
    except Exception as e:
        logger.error(f"获取投放账户列表失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/accounts/update-versions", response_model=ApiResponse)
async def update_account_versions(db: AdDatabase = Depends(lambda: AdDatabase())):
    """更新投放账户的版本信息"""
    try:
        # 获取小游戏版本信息
        game_versions = await db.get_game_versions()

        if not game_versions:
            return ApiResponse(
                code=20000,
                message="没有找到小游戏版本信息",
                data={"updated": 0}
            )
        
        # 更新投放账户的版本信息
        updated_count = 0
        async with db._get_connection() as conn:
            # 获取所有投放账户
            cursor = await conn.execute(
                f"SELECT id, appId FROM {db.accounts_table} WHERE appId IS NOT NULL"
            )
            accounts = await cursor.fetchall()
            total_accounts = len([account for account in accounts])

            # 更新每个账户的版本信息
            for account in accounts:
                account_id = account[0]
                app_id = account[1]

                if app_id in game_versions:
                    version = game_versions[app_id]
                    await conn.execute(
                        f"UPDATE {db.accounts_table} SET onlineVersion = ? WHERE id = ?",
                        (version, account_id)
                    )
                    updated_count += 1

            await conn.commit()

        return ApiResponse(
            code=20000,
            message=f"成功更新{updated_count}个账户的版本信息",
            data={"updated": updated_count, "total": total_accounts}
        )
    except Exception as e:
        logger.error(f"更新投放账户版本信息失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))
