#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from fastapi import APIRouter, Query, BackgroundTasks
from typing import Optional, List
import random
from datetime import datetime, timedelta
from src.models.response import (
    ApiResponse, PaginatedResponse,
    ArticleItem, RoleItem, UserSearchItem
)
from src.models.config import (
    FilterConfigCreate, FilterConfigUpdate, FilterConfigResponse
)
from src.utils.ad_database import ad_database
from src.services.data_fetcher import data_fetcher

router = APIRouter(prefix="/api/vue-element-admin", tags=["Vue Element Admin"])

@router.get("/transaction/list", summary="投放数据列表")
async def get_transaction_list(
    page: int = Query(1, ge=1, description="页码"),
    limit: int = Query(20, ge=1, le=100, description="每页数量"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    platform: Optional[str] = Query(None, description="投放平台"),
    status: Optional[str] = Query(None, description="状态")
):
    """获取投放数据列表"""
    try:
        result = await ad_database.get_accounts(
            page=page,
            limit=limit,
            search=search,
            platform=platform,
            status=status
        )

        return ApiResponse(
            code=20000,
            message="获取成功",
            data=result
        )
    except Exception as e:
        return ApiResponse(
            code=500,
            message=f"获取投放数据失败: {str(e)}",
            data=None
        )

@router.get("/games/list", summary="小游戏数据列表")
async def get_games_list(
    page: int = Query(1, ge=1, description="页码"),
    limit: int = Query(20, ge=1, le=100, description="每页数量"),
    search: Optional[str] = Query(None, description="搜索关键词")
):
    """获取小游戏数据列表"""
    try:
        result = await ad_database.get_games(
            page=page,
            limit=limit,
            search=search
        )

        return ApiResponse(
            code=20000,
            message="获取成功",
            data=result
        )
    except Exception as e:
        return ApiResponse(
            code=500,
            message=f"获取小游戏数据失败: {str(e)}",
            data=None
        )

@router.get("/stats", summary="统计数据")
async def get_stats():
    """获取统计数据"""
    try:
        result = await ad_database.get_stats()

        return ApiResponse(
            code=20000,
            message="获取成功",
            data=result
        )
    except Exception as e:
        return ApiResponse(
            code=500,
            message=f"获取统计数据失败: {str(e)}",
            data=None
        )

@router.post("/fetch-ad-data", summary="获取投放数据")
async def fetch_ad_data(
    background_tasks: BackgroundTasks,
    days_ago: int = Query(7, ge=1, le=30, description="获取多少天前的数据"),
    user_ids: Optional[List[int]] = Query(None, description="指定用户ID列表"),
    clear_old_data: bool = Query(True, description="是否清空旧数据，默认True")
):
    """从外部API获取投放数据

    Args:
        days_ago: 获取多少天前的数据
        user_ids: 指定用户ID列表
        clear_old_data: 是否清空旧数据，默认True
    """
    try:
        # 在后台任务中执行数据获取
        background_tasks.add_task(
            data_fetcher.fetch_ad_accounts,
            days_ago=days_ago,
            user_ids=user_ids,
            clear_old_data=clear_old_data
        )

        return ApiResponse(
            code=20000,
            message="数据获取任务已启动，请稍后查看结果",
            data={"days_ago": days_ago, "user_ids": user_ids, "clear_old_data": clear_old_data}
        )
    except Exception as e:
        return ApiResponse(
            code=500,
            message=f"启动数据获取任务失败: {str(e)}",
            data=None
        )

@router.post("/fetch-game-data", summary="获取小游戏数据")
async def fetch_game_data(
    background_tasks: BackgroundTasks,
    days_ago: int = Query(7, ge=1, le=30, description="获取多少天前的数据"),
    user_ids: Optional[List[int]] = Query(None, description="指定用户ID列表"),
    clear_old_data: bool = Query(True, description="是否清空旧数据，默认True")
):
    """从外部API获取小游戏数据

    Args:
        days_ago: 获取多少天前的数据
        user_ids: 指定用户ID列表
        clear_old_data: 是否清空旧数据，默认True
    """
    try:
        # 在后台任务中执行数据获取
        background_tasks.add_task(
            data_fetcher.fetch_game_data,
            days_ago=days_ago,
            user_ids=user_ids,
            clear_old_data=clear_old_data
        )

        return ApiResponse(
            code=20000,
            message="小游戏数据获取任务已启动，请稍后查看结果",
            data={"days_ago": days_ago, "user_ids": user_ids, "clear_old_data": clear_old_data}
        )
    except Exception as e:
        return ApiResponse(
            code=500,
            message=f"启动小游戏数据获取任务失败: {str(e)}",
            data=None
        )

@router.get("/search/user", response_model=ApiResponse[PaginatedResponse[UserSearchItem]], summary="用户搜索")
async def search_user(name: Optional[str] = Query(None, description="搜索的用户名")):
    """用户搜索接口"""
    # 模拟用户数据
    users = [
        UserSearchItem(name='admin'),
        UserSearchItem(name='editor'),
        UserSearchItem(name='user1'),
        UserSearchItem(name='user2'),
        UserSearchItem(name='manager'),
        UserSearchItem(name='developer')
    ]
    
    if name:
        users = [user for user in users if name.lower() in user.name.lower()]
    
    return ApiResponse(
        code=20000,
        message="搜索成功",
        data=PaginatedResponse(total=len(users), items=users)
    )

@router.get("/article/list", response_model=ApiResponse[PaginatedResponse[ArticleItem]], summary="文章列表")
async def get_article_list():
    """获取文章列表"""
    # 生成模拟文章数据
    articles = []
    for i in range(20):
        articles.append(ArticleItem(
            id=i + 1,
            timestamp=int((datetime.now() - timedelta(days=random.randint(0, 30))).timestamp() * 1000),
            author=f'Author{random.randint(1, 10)}',
            reviewer=f'Reviewer{random.randint(1, 5)}',
            title=f'Article Title {i + 1}',
            content_short='This is a mock article content...',
            forecast=round(random.uniform(0, 100), 2),
            importance=random.randint(1, 3),
            type=random.choice(['CN', 'US', 'JP', 'EU']),
            status=random.choice(['published', 'draft']),
            display_time=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            comment_disabled=random.choice([True, False]),
            pageviews=random.randint(300, 5000),
            platforms=['a-platform']
        ))
    
    return ApiResponse(
        code=20000,
        message="获取成功",
        data=PaginatedResponse(total=20, items=articles)
    )

@router.get("/roles", response_model=ApiResponse[PaginatedResponse[RoleItem]], summary="角色列表")
async def get_roles():
    """获取角色列表"""
    roles = [
        RoleItem(
            key='admin',
            name='admin',
            description='Super Administrator. Have access to view all pages.',
            routes=[]
        ),
        RoleItem(
            key='editor',
            name='editor',
            description='Normal Editor. Can see all pages except permission page',
            routes=[]
        )
    ]
    
    return ApiResponse(
        code=20000,
        message="获取成功",
        data=PaginatedResponse(total=len(roles), items=roles)
    )

# ==================== 配置管理相关接口 ====================

@router.get("/filter-configs", summary="获取筛选配置列表")
async def get_filter_configs(
    page: int = Query(1, ge=1, description="页码"),
    limit: int = Query(20, ge=1, le=100, description="每页数量"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    all: bool = Query(False, description="是否获取全部数据（忽略分页）")
):
    """获取筛选配置列表"""
    try:
        result = await ad_database.get_filter_configs(
            page=page,
            limit=limit,
            search=search,
            get_all=all
        )

        return ApiResponse(
            code=20000,
            message="获取成功",
            data=result
        )
    except Exception as e:
        return ApiResponse(
            code=500,
            message=f"获取筛选配置失败: {str(e)}",
            data=None
        )

@router.get("/filter-configs/all", summary="获取全部筛选配置")
async def get_all_filter_configs(
    search: Optional[str] = Query(None, description="搜索关键词")
):
    """获取全部筛选配置（不分页）"""
    try:
        result = await ad_database.get_all_filter_configs(search=search)

        return ApiResponse(
            code=20000,
            message="获取成功",
            data=result
        )
    except Exception as e:
        return ApiResponse(
            code=500,
            message=f"获取全部筛选配置失败: {str(e)}",
            data=None
        )

@router.post("/filter-configs", summary="创建筛选配置")
async def create_filter_config(config_data: dict):
    """创建筛选配置"""
    try:
        # 验证必需字段
        if not config_data.get('appId'):
            return ApiResponse(
                code=400,
                message="应用ID不能为空",
                data=None
            )

        # 检查appId是否已存在
        app_id = config_data.get('appId')
        if app_id:
            existing_config = await ad_database.get_config_by_appid(app_id)
            if existing_config:
                return ApiResponse(
                    code=400,
                    message="一个appid只能创建一个配置，该应用ID已存在配置",
                    data=None
                )

        success = await ad_database.create_filter_config(config_data)

        if success:
            return ApiResponse(
                code=20000,
                message="创建成功",
                data=None
            )
        else:
            return ApiResponse(
                code=500,
                message="创建失败",
                data=None
            )
    except Exception as e:
        return ApiResponse(
            code=500,
            message=f"创建筛选配置失败: {str(e)}",
            data=None
        )

@router.put("/filter-configs/{config_id}", summary="更新筛选配置")
async def update_filter_config(config_id: int, config_data: dict):
    """更新筛选配置"""
    try:
        success = await ad_database.update_filter_config(config_id, config_data)

        if success:
            return ApiResponse(
                code=20000,
                message="更新成功",
                data=None
            )
        else:
            return ApiResponse(
                code=500,
                message="更新失败",
                data=None
            )
    except Exception as e:
        return ApiResponse(
            code=500,
            message=f"更新筛选配置失败: {str(e)}",
            data=None
        )

@router.delete("/filter-configs/{config_id}", summary="删除筛选配置")
async def delete_filter_config(config_id: int):
    """删除筛选配置"""
    try:
        success = await ad_database.delete_filter_config(config_id)

        if success:
            return ApiResponse(
                code=20000,
                message="删除成功",
                data=None
            )
        else:
            return ApiResponse(
                code=500,
                message="删除失败",
                data=None
            )
    except Exception as e:
        return ApiResponse(
            code=500,
            message=f"删除筛选配置失败: {str(e)}",
            data=None
        )








