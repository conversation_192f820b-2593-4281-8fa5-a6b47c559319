#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from fastapi import APIRouter, HTTPException, status, Depends
from src.models.user import UserLogin, UserResponse, Token
from src.models.response import ApiResponse
from src.utils.database import database
from src.utils.auth import create_access_token, get_current_active_user

router = APIRouter(prefix="/api/user", tags=["用户管理"])

@router.post("/login", summary="用户登录")
async def login(user_credentials: UserLogin):
    """用户登录接口"""
    # 获取用户信息
    user = await database.get_user_by_username(user_credentials.username)
    
    if not user or not database.verify_password(user_credentials.password, user['password']):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户名或密码错误"
        )
    
    # 创建访问令牌
    access_token = create_access_token(data={"sub": str(user['id'])})
    
    return ApiResponse(
        code=20000,
        message="登录成功",
        data={"token": access_token}
    )

@router.get("/info", response_model=ApiResponse[UserResponse], summary="获取用户信息")
async def get_user_info(current_user = Depends(get_current_active_user)):
    """获取当前用户信息"""
    user_data = {
        "id": current_user['id'],
        "username": current_user['username'],
        "email": current_user['email'],
        "role": current_user['role'],
        "avatar": current_user['avatar'] or 'https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif',
        "introduction": current_user['introduction'],
        "roles": [current_user['role']]
    }
    
    return ApiResponse(
        code=20000,
        message="获取成功",
        data=UserResponse(**user_data)
    )

@router.post("/logout", response_model=ApiResponse[str], summary="用户登出")
async def logout(current_user = Depends(get_current_active_user)):
    """用户登出接口"""
    return ApiResponse(
        code=20000,
        message="登出成功",
        data="success"
    )
