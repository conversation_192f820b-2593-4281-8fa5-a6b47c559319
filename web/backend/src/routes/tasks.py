#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from fastapi import APIRouter, Query, HTTPException, BackgroundTasks, Request
from typing import Optional, List
from datetime import datetime

from src.models.response import ApiResponse, PaginatedResponse
from src.models.task import (
    TaskCreate, TaskUpdate, TaskResponse, TaskExecuteRequest,
    TaskLogResponse, TaskStatsResponse, TaskStatus, TaskType
)
from src.utils.ad_database import ad_database
from src.services.task_scheduler import task_scheduler

router = APIRouter(prefix="/api/vue-element-admin", tags=["任务管理"])

# ==================== 任务视图管理 ====================

@router.get("/tasks/views", summary="获取任务视图列表")
async def get_task_views():
    """获取所有任务视图"""
    try:
        views = await ad_database.get_task_views()
        return ApiResponse(
            code=20000,
            message="获取成功",
            data=views
        )
    except Exception as e:
        return ApiResponse(
            code=500,
            message=f"获取任务视图失败: {str(e)}",
            data=None
        )

@router.post("/tasks/views", summary="创建任务视图")
async def create_task_view(request: Request):
    """创建新的任务视图"""
    try:
        data = await request.json()
        name = data.get('name')
        description = data.get('description', '')
        filter_type = data.get('filter_type', 'path')
        filter_value = data.get('filter_value', '')

        if not name:
            return ApiResponse(
                code=400,
                message="视图名称不能为空",
                data=None
            )

        view_id = await ad_database.create_task_view(name, description, filter_type, filter_value)
        return ApiResponse(
            code=20000,
            message="创建成功",
            data={"id": view_id}
        )
    except Exception as e:
        return ApiResponse(
            code=500,
            message=f"创建任务视图失败: {str(e)}",
            data=None
        )

@router.put("/tasks/views/{view_id}", summary="更新任务视图")
async def update_task_view(view_id: int, request: Request):
    """更新任务视图"""
    try:
        data = await request.json()
        name = data.get('name')
        description = data.get('description', '')
        filter_type = data.get('filter_type', 'path')
        filter_value = data.get('filter_value', '')

        if not name:
            return ApiResponse(
                code=400,
                message="视图名称不能为空",
                data=None
            )

        await ad_database.update_task_view(view_id, name, description, filter_type, filter_value)
        return ApiResponse(
            code=20000,
            message="更新成功",
            data=None
        )
    except Exception as e:
        return ApiResponse(
            code=500,
            message=f"更新任务视图失败: {str(e)}",
            data=None
        )

@router.delete("/tasks/views/{view_id}", summary="删除任务视图")
async def delete_task_view(view_id: int):
    """删除任务视图"""
    try:
        await ad_database.delete_task_view(view_id)
        return ApiResponse(
            code=20000,
            message="删除成功",
            data=None
        )
    except ValueError as e:
        return ApiResponse(
            code=400,
            message=str(e),
            data=None
        )
    except Exception as e:
        return ApiResponse(
            code=500,
            message=f"删除任务视图失败: {str(e)}",
            data=None
        )

@router.get("/tasks/views/{view_id}/tasks", summary="根据视图获取任务列表")
async def get_tasks_by_view(view_id: int, page: int = 1, limit: int = 20):
    """根据视图获取任务列表"""
    try:
        result = await ad_database.get_tasks_by_view(view_id, page, limit)
        return ApiResponse(
            code=20000,
            message="获取成功",
            data=result
        )
    except ValueError as e:
        return ApiResponse(
            code=404,
            message=str(e),
            data=None
        )
    except Exception as e:
        return ApiResponse(
            code=500,
            message=f"获取任务列表失败: {str(e)}",
            data=None
        )

# ==================== 任务管理接口 ====================

@router.get("/tasks/stats", summary="获取任务统计")
async def get_task_stats():
    """获取任务统计"""
    try:
        # 获取所有任务进行统计
        all_tasks = await ad_database.get_tasks(page=1, limit=1000)
        tasks = all_tasks['items']

        total_tasks = len(tasks)
        enabled_tasks = len([t for t in tasks if t['enabled']])
        running_tasks = len([t for t in tasks if t['status'] == 'running'])

        # 计算成功率
        total_runs = sum(t['run_count'] for t in tasks)
        total_success = sum(t['success_count'] for t in tasks)
        success_rate = round((total_success / total_runs) * 100, 1) if total_runs > 0 else 0

        stats = {
            'total_tasks': total_tasks,
            'enabled_tasks': enabled_tasks,
            'running_tasks': running_tasks,
            'success_rate': success_rate,
            'total_executions': total_runs
        }

        return ApiResponse(
            code=20000,
            message="获取成功",
            data=stats
        )
    except Exception as e:
        return ApiResponse(
            code=500,
            message=f"获取任务统计失败: {str(e)}",
            data=None
        )

@router.get("/tasks", summary="获取任务列表")
async def get_tasks(
    page: int = Query(1, ge=1, description="页码"),
    limit: int = Query(20, ge=1, le=100, description="每页数量"),
    search: Optional[str] = Query(None, description="搜索关键词")
):
    """获取任务列表"""
    try:
        result = await ad_database.get_tasks(
            page=page,
            limit=limit,
            search=search
        )

        return ApiResponse(
            code=20000,
            message="获取成功",
            data=result
        )
    except Exception as e:
        return ApiResponse(
            code=500,
            message=f"获取任务列表失败: {str(e)}",
            data=None
        )

@router.post("/tasks", summary="创建任务")
async def create_task(task_data: TaskCreate):
    """创建任务"""
    try:
        # 验证任务类型和参数
        if task_data.task_type == TaskType.CRON and not task_data.cron_expression:
            return ApiResponse(
                code=400,
                message="Cron任务必须提供cron表达式",
                data=None
            )
        
        if task_data.task_type == TaskType.INTERVAL and not task_data.interval_seconds:
            return ApiResponse(
                code=400,
                message="间隔任务必须提供间隔秒数",
                data=None
            )
        
        if task_data.task_type == TaskType.DATE and not task_data.run_date:
            return ApiResponse(
                code=400,
                message="一次性任务必须提供运行日期",
                data=None
            )

        # 创建任务
        success = await ad_database.create_task(task_data.model_dump())
        
        if success:
            # 如果任务启用，添加到调度器
            if task_data.enabled:
                # 获取刚创建的任务
                tasks = await ad_database.get_tasks(search=task_data.name)
                if tasks['items']:
                    task = tasks['items'][0]
                    await task_scheduler.add_task(task)
            
            return ApiResponse(
                code=20000,
                message="创建成功",
                data=None
            )
        else:
            return ApiResponse(
                code=500,
                message="创建失败",
                data=None
            )
    except Exception as e:
        return ApiResponse(
            code=500,
            message=f"创建任务失败: {str(e)}",
            data=None
        )

@router.get("/tasks/{task_id}", summary="获取任务详情")
async def get_task(task_id: int):
    """获取任务详情"""
    try:
        task = await ad_database.get_task_by_id(task_id)
        if not task:
            return ApiResponse(
                code=404,
                message="任务不存在",
                data=None
            )
        
        return ApiResponse(
            code=20000,
            message="获取成功",
            data=task
        )
    except Exception as e:
        return ApiResponse(
            code=500,
            message=f"获取任务详情失败: {str(e)}",
            data=None
        )

@router.put("/tasks/{task_id}", summary="更新任务")
async def update_task(task_id: int, task_data: TaskUpdate):
    """更新任务"""
    try:
        # 检查任务是否存在
        existing_task = await ad_database.get_task_by_id(task_id)
        if not existing_task:
            return ApiResponse(
                code=404,
                message="任务不存在",
                data=None
            )

        # 更新任务
        update_data = {k: v for k, v in task_data.model_dump().items() if v is not None}
        success = await ad_database.update_task(task_id, update_data)

        if success:
            # 重新加载任务到调度器
            await task_scheduler.remove_task(task_id)
            
            # 获取更新后的任务
            updated_task = await ad_database.get_task_by_id(task_id)
            if updated_task and updated_task['enabled']:
                await task_scheduler.add_task(updated_task)
            
            return ApiResponse(
                code=20000,
                message="更新成功",
                data=None
            )
        else:
            return ApiResponse(
                code=500,
                message="更新失败",
                data=None
            )
    except Exception as e:
        return ApiResponse(
            code=500,
            message=f"更新任务失败: {str(e)}",
            data=None
        )

@router.delete("/tasks/{task_id}", summary="删除任务")
async def delete_task(task_id: int):
    """删除任务"""
    try:
        # 检查任务是否存在
        existing_task = await ad_database.get_task_by_id(task_id)
        if not existing_task:
            return ApiResponse(
                code=404,
                message="任务不存在",
                data=None
            )

        # 从调度器中移除任务
        await task_scheduler.remove_task(task_id)
        
        # 删除任务
        success = await ad_database.delete_task(task_id)

        if success:
            return ApiResponse(
                code=20000,
                message="删除成功",
                data=None
            )
        else:
            return ApiResponse(
                code=500,
                message="删除失败",
                data=None
            )
    except Exception as e:
        return ApiResponse(
            code=500,
            message=f"删除任务失败: {str(e)}",
            data=None
        )

@router.post("/tasks/{task_id}/run_now", summary="立即执行任务")
async def run_task_now(task_id: int, request: TaskExecuteRequest = TaskExecuteRequest()):
    """立即执行任务"""
    try:
        # 检查任务是否存在
        task = await ad_database.get_task_by_id(task_id)
        if not task:
            return ApiResponse(
                code=404,
                message="任务不存在",
                data=None
            )

        # 立即执行任务
        success = await task_scheduler.run_task_now(task, request.env_vars)

        if success:
            return ApiResponse(
                code=20000,
                message="任务已开始执行",
                data=None
            )
        else:
            return ApiResponse(
                code=500,
                message="执行任务失败",
                data=None
            )
    except Exception as e:
        return ApiResponse(
            code=500,
            message=f"执行任务失败: {str(e)}",
            data=None
        )

@router.post("/tasks/{task_id}/pause", summary="暂停任务")
async def pause_task(task_id: int):
    """暂停任务"""
    try:
        success = await task_scheduler.pause_task(task_id)
        if success:
            # 更新数据库状态
            await ad_database.update_task(task_id, {'enabled': False})
            return ApiResponse(
                code=20000,
                message="任务已暂停",
                data=None
            )
        else:
            return ApiResponse(
                code=500,
                message="暂停任务失败",
                data=None
            )
    except Exception as e:
        return ApiResponse(
            code=500,
            message=f"暂停任务失败: {str(e)}",
            data=None
        )

@router.post("/tasks/{task_id}/resume", summary="恢复任务")
async def resume_task(task_id: int):
    """恢复任务"""
    try:
        success = await task_scheduler.resume_task(task_id)
        if success:
            # 更新数据库状态
            await ad_database.update_task(task_id, {'enabled': True})
            return ApiResponse(
                code=20000,
                message="任务已恢复",
                data=None
            )
        else:
            return ApiResponse(
                code=500,
                message="恢复任务失败",
                data=None
            )
    except Exception as e:
        return ApiResponse(
            code=500,
            message=f"恢复任务失败: {str(e)}",
            data=None
        )

@router.get("/tasks/{task_id}/logs", summary="获取任务日志")
async def get_task_logs(
    task_id: int,
    page: int = Query(1, ge=1, description="页码"),
    limit: int = Query(20, ge=1, le=100, description="每页数量")
):
    """获取任务日志"""
    try:
        result = await ad_database.get_task_logs(task_id, page, limit)
        return ApiResponse(
            code=20000,
            message="获取成功",
            data=result
        )
    except Exception as e:
        return ApiResponse(
            code=500,
            message=f"获取任务日志失败: {str(e)}",
            data=None
        )

@router.get("/tasks/{task_id}/logs/latest", summary="获取任务最新日志")
async def get_latest_task_log(task_id: int):
    """获取任务最新执行日志（用于实时显示）"""
    try:
        # 获取最新的一条日志记录
        result = await ad_database.get_task_logs(task_id, 1, 1)
        if result and result.get('items'):
            latest_log = result['items'][0]
            return ApiResponse(
                code=20000,
                message="获取成功",
                data=latest_log
            )
        else:
            return ApiResponse(
                code=20000,
                message="暂无日志",
                data=None
            )
    except Exception as e:
        return ApiResponse(
            code=500,
            message=f"获取最新任务日志失败: {str(e)}",
            data=None
        )

@router.get("/tasks/{task_id}/status", summary="获取任务运行状态")
async def get_task_status(task_id: int):
    """获取任务当前运行状态"""
    try:
        task = await ad_database.get_task_by_id(task_id)
        if not task:
            return ApiResponse(
                code=404,
                message="任务不存在",
                data=None
            )

        # 获取最新日志来判断运行状态
        result = await ad_database.get_task_logs(task_id, 1, 1)
        latest_log = None
        if result and result.get('items'):
            latest_log = result['items'][0]

        return ApiResponse(
            code=20000,
            message="获取成功",
            data={
                "task_id": task_id,
                "task_status": task.get('status'),
                "latest_log": latest_log,
                "is_running": task.get('status') == 'running'
            }
        )
    except Exception as e:
        return ApiResponse(
            code=500,
            message=f"获取任务状态失败: {str(e)}",
            data=None
        )


@router.post("/tasks/{task_id}/stop", summary="停止任务")
async def stop_task(task_id: int):
    """停止正在运行的任务"""
    try:
        task = await ad_database.get_task_by_id(task_id)
        if not task:
            return ApiResponse(
                code=404,
                message="任务不存在",
                data=None
            )

        # 停止任务
        success = await task_scheduler.stop_task(task_id)

        if success:
            return ApiResponse(
                code=20000,
                message="任务已停止",
                data=None
            )
        else:
            return ApiResponse(
                code=400,
                message="任务未在运行或停止失败",
                data=None
            )
    except Exception as e:
        return ApiResponse(
            code=500,
            message=f"停止任务失败: {str(e)}",
            data=None
        )


