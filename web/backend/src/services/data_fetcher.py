#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
数据获取服务模块
负责从外部API获取投放数据和小游戏数据
"""

import asyncio
import aiohttp
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Any
from src.utils.ad_database import ad_database

logger = logging.getLogger(__name__)

class DataFetcher:
    """数据获取服务类"""
    
    def __init__(self):
        # 通用配置
        self.user_ids = [103, 104, 107, 108, 110, 111, 113, 114, 116, 120]
        self.page_size = 1000
        self.auth_token = None
        self.base_url = "http://game.raisedsun.com/prod-api"
        self.auth_url = "http://124.221.208.141:5000/get/tfpt"
        self.auth_headers = {'Authorization': 'your_secret_token_123'}
    
    async def get_authorization(self) -> str:
        """获取授权token"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(self.auth_url, headers=self.auth_headers) as response:
                    if response.status == 200:
                        self.auth_token = await response.text()
                        logger.info("获取授权token成功")
                        return self.auth_token
                    else:
                        raise Exception(f"获取授权失败: {response.status}")
        except Exception as e:
            logger.error(f"获取授权token失败: {e}")
            raise
    
    async def fetch_with_retry(self, session: aiohttp.ClientSession, url: str, 
                              headers: dict, max_retries: int = 3) -> List[Dict]:
        """带重试机制的请求函数"""
        retry_count = 0
        retry_delay = 2
        
        while retry_count < max_retries:
            try:
                async with session.get(url, headers=headers, ssl=False, timeout=30) as response:
                    if response.status == 200:
                        data = await response.json()
                        if 'rows' in data:
                            return data['rows']
                        elif 'data' in data:
                            return data['data']
                        elif 'list' in data:
                            return data['list']
                        return []
                    else:
                        logger.warning(f"请求失败: {response.status}, URL: {url}")
                        if 500 <= response.status < 600:
                            retry_count += 1
                            if retry_count < max_retries:
                                await asyncio.sleep(retry_delay)
                                retry_delay *= 2
                                continue
                        return []
            except Exception as e:
                logger.error(f"请求错误: {e}, URL: {url}")
                retry_count += 1
                if retry_count < max_retries:
                    await asyncio.sleep(retry_delay)
                    retry_delay *= 2
                    continue
                return []
        
        logger.error(f"请求在{max_retries}次重试后仍然失败: {url}")
        return []
    
    async def get_game_versions(self) -> Dict[str, str]:
        """获取小游戏版本映射"""
        try:
            if not self.auth_token:
                await self.get_authorization()
            
            url = f"{self.base_url}/weapp-account/simpleList?pageNum=1&pageSize=5000&onlineVersionFlag=1"
            headers = {
                'Accept': 'application/json, text/plain, */*',
                'Authorization': self.auth_token,
                'Cookie': f'Admin-Token={self.auth_token}; sidebarStatus=1'
            }
            
            async with aiohttp.ClientSession() as session:
                game_list = await self.fetch_with_retry(session, url, headers)
            
            # 创建appId到onlineVersion的映射
            version_map = {}
            for game in game_list:
                appId = game.get('appId')
                onlineVersion = game.get('onlineVersion')
                if appId and onlineVersion:
                    version_map[appId] = onlineVersion
            
            logger.info(f"获取到{len(version_map)}个小游戏版本映射")
            return version_map
            
        except Exception as e:
            logger.error(f"获取小游戏版本失败: {e}")
            raise
    
    async def fetch_ad_accounts(self, days_ago: int = 7, user_ids: Optional[List[int]] = None,
                               clear_old_data: bool = True) -> Dict[str, Any]:
        """获取投放账户数据

        Args:
            days_ago: 获取多少天前的数据
            user_ids: 用户ID列表
            clear_old_data: 是否清空旧数据，默认True
        """
        try:
            if not self.auth_token:
                await self.get_authorization()

            # 设置时间范围
            end_date = datetime.today()
            start_date = end_date - timedelta(days=days_ago)
            start_date_str = start_date.strftime('%Y-%m-%d')
            end_date_str = end_date.strftime('%Y-%m-%d')

            # 使用传入的user_ids或默认值
            target_user_ids = user_ids or self.user_ids
            
            headers = {
                'Accept': 'application/json, text/plain, */*',
                'Authorization': self.auth_token,
                'Cookie': f'Admin-Token={self.auth_token}; sidebarStatus=1'
            }
            
            all_accounts = []
            
            async with aiohttp.ClientSession() as session:
                for user_id in target_user_ids:
                    url = f"{self.base_url}/ad/account/list?pageNum=1&pageSize={self.page_size}&groupId=-1&userId={user_id}&isOutBalance=false&startDate={start_date_str}&endDate={end_date_str}"
                    
                    accounts = await self.fetch_with_retry(session, url, headers)
                    
                    # 过滤有效账户（有appId的）
                    valid_accounts = [acc for acc in accounts if acc.get('appId')]
                    all_accounts.extend(valid_accounts)
                    
                    logger.info(f"UserID [{user_id}] 获取到{len(valid_accounts)}条有效账户数据")
            
            # 保存到数据库
            saved_count = await ad_database.save_accounts(all_accounts, start_date_str, end_date_str, clear_old_data)
            
            return {
                "total_accounts": len(all_accounts),
                "saved_count": saved_count,
                "start_date": start_date_str,
                "end_date": end_date_str,
                "user_ids": target_user_ids
            }
            
        except Exception as e:
            logger.error(f"获取投放账户数据失败: {e}")
            raise
    
    async def fetch_game_data(self, days_ago: int = 7, user_ids: Optional[List[int]] = None,
                             clear_old_data: bool = True) -> Dict[str, Any]:
        """获取小游戏数据

        Args:
            days_ago: 获取多少天前的数据
            user_ids: 用户ID列表
            clear_old_data: 是否清空旧数据，默认True
        """
        try:
            if not self.auth_token:
                await self.get_authorization()
            
            # 设置时间范围
            end_date = datetime.today()
            start_date = end_date - timedelta(days=days_ago)
            start_date_str = start_date.strftime('%Y-%m-%d')
            end_date_str = end_date.strftime('%Y-%m-%d')
            
            # 获取游戏版本映射
            version_map = await self.get_game_versions()
            
            # 使用传入的user_ids或默认值
            target_user_ids = user_ids or self.user_ids
            
            headers = {
                'Accept': 'application/json, text/plain, */*',
                'Authorization': self.auth_token,
                'Cookie': f'Admin-Token={self.auth_token}; sidebarStatus=1'
            }
            
            all_games = []
            
            async with aiohttp.ClientSession() as session:
                for user_id in target_user_ids:
                    url = f"{self.base_url}/ad/game/list?groupId=-1&pageNum=1&pageSize={self.page_size}&sortField=todayCost&sortOrder=desc&startDate={start_date_str}&endDate={end_date_str}&userId={user_id}"
                    
                    games = await self.fetch_with_retry(session, url, headers)
                    
                    # 添加版本信息
                    for game in games:
                        appId = game.get('appId')
                        if appId in version_map:
                            game['onlineVersion'] = version_map[appId]
                        else:
                            game['onlineVersion'] = ''
                    
                    all_games.extend(games)
                    logger.info(f"UserID [{user_id}] 获取到{len(games)}条游戏数据")
            
            # 保存到数据库
            saved_count = await ad_database.save_games(all_games, start_date_str, end_date_str, clear_old_data)
            
            return {
                "total_games": len(all_games),
                "saved_count": saved_count,
                "start_date": start_date_str,
                "end_date": end_date_str,
                "user_ids": target_user_ids
            }
            
        except Exception as e:
            logger.error(f"获取小游戏数据失败: {e}")
            raise

# 创建数据获取服务实例
data_fetcher = DataFetcher()
