#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import asyncio
import subprocess
import os
import json
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.cron import Cron<PERSON>rigger
from apscheduler.triggers.interval import IntervalTrigger
from apscheduler.triggers.date import DateTrigger
from apscheduler.events import EVENT_JOB_EXECUTED, EVENT_JOB_ERROR, EVENT_JOB_ADDED, EVENT_JOB_REMOVED
from apscheduler.jobstores.memory import MemoryJobStore
from apscheduler.executors.asyncio import AsyncIOExecutor

from src.models.task import TaskStatus, TaskType
from src.utils.ad_database import ad_database

logger = logging.getLogger(__name__)

class TaskScheduler:
    """任务调度器"""
    
    def __init__(self):
        # 配置调度器
        jobstores = {
            'default': MemoryJobStore()
        }
        executors = {
            'default': AsyncIOExecutor()
        }
        job_defaults = {
            'coalesce': False,
            'max_instances': 1,
            'misfire_grace_time': 30
        }
        
        self.scheduler = AsyncIOScheduler(
            jobstores=jobstores,
            executors=executors,
            job_defaults=job_defaults,
            timezone='Asia/Shanghai'
        )
        
        # 添加事件监听器
        self.scheduler.add_listener(self._job_executed, EVENT_JOB_EXECUTED)
        self.scheduler.add_listener(self._job_error, EVENT_JOB_ERROR)
        
        self.running_tasks: Dict[str, Dict[str, Any]] = {}
        self.running_processes: Dict[int, asyncio.subprocess.Process] = {}
    
    async def start(self):
        """启动调度器"""
        try:
            self.scheduler.start()
            logger.info("任务调度器启动成功")
            
            # 从数据库加载已有任务
            await self._load_tasks_from_db()
            
        except Exception as e:
            logger.error(f"任务调度器启动失败: {e}")
            raise
    
    async def shutdown(self):
        """关闭调度器"""
        try:
            self.scheduler.shutdown(wait=False)
            logger.info("任务调度器已关闭")
        except Exception as e:
            logger.error(f"任务调度器关闭失败: {e}")
    
    async def add_task(self, task_data: Dict[str, Any]) -> bool:
        """添加任务"""
        try:
            task_id = task_data['id']
            job_id = f"task_{task_id}"

            # 根据任务类型创建触发器
            trigger = self._create_trigger(task_data)
            if not trigger:
                return False

            # 添加任务到调度器
            job = self.scheduler.add_job(
                func=self._execute_task,
                trigger=trigger,
                id=job_id,
                args=[task_data],
                max_instances=task_data.get('max_instances', 1),
                replace_existing=True
            )

            # 更新数据库中的下次运行时间
            if job.next_run_time:
                next_run_time = job.next_run_time.strftime('%Y-%m-%d %H:%M:%S')
                await ad_database.update_task_next_run_time(task_id, next_run_time)

            logger.info(f"任务 {task_data['name']} 添加成功")
            return True

        except Exception as e:
            logger.error(f"添加任务失败: {e}")
            return False
    
    async def remove_task(self, task_id: int) -> bool:
        """移除任务"""
        try:
            job_id = f"task_{task_id}"
            self.scheduler.remove_job(job_id)
            logger.info(f"任务 {task_id} 移除成功")
            return True
        except Exception as e:
            logger.error(f"移除任务失败: {e}")
            return False
    
    async def pause_task(self, task_id: int) -> bool:
        """暂停任务"""
        try:
            job_id = f"task_{task_id}"
            self.scheduler.pause_job(job_id)
            logger.info(f"任务 {task_id} 暂停成功")
            return True
        except Exception as e:
            logger.error(f"暂停任务失败: {e}")
            return False
    
    async def resume_task(self, task_id: int) -> bool:
        """恢复任务"""
        try:
            job_id = f"task_{task_id}"
            self.scheduler.resume_job(job_id)
            logger.info(f"任务 {task_id} 恢复成功")
            return True
        except Exception as e:
            logger.error(f"恢复任务失败: {e}")
            return False
    
    async def run_task_now(self, task_data: Dict[str, Any], env_vars: Optional[Dict[str, str]] = None) -> bool:
        """立即执行任务"""
        try:
            # 合并环境变量
            if env_vars:
                task_data = task_data.copy()
                task_data['env_vars'] = {**(task_data.get('env_vars') or {}), **env_vars}
            
            # 创建异步任务执行
            asyncio.create_task(self._execute_task(task_data, is_manual=True))
            return True
        except Exception as e:
            logger.error(f"立即执行任务失败: {e}")
            return False
    
    def _create_trigger(self, task_data: Dict[str, Any]):
        """创建触发器"""
        task_type = task_data.get('task_type')
        
        if task_type == TaskType.CRON:
            cron_expr = task_data.get('cron_expression')
            if cron_expr:
                return CronTrigger.from_crontab(cron_expr)
        
        elif task_type == TaskType.INTERVAL:
            interval_seconds = task_data.get('interval_seconds')
            if interval_seconds:
                return IntervalTrigger(seconds=interval_seconds)
        
        elif task_type == TaskType.DATE:
            run_date = task_data.get('run_date')
            if run_date:
                return DateTrigger(run_date=run_date)
        
        return None
    
    async def stop_task(self, task_id: int) -> bool:
        """停止正在运行的任务"""
        try:
            # 检查任务是否在运行
            task = await ad_database.get_task_by_id(task_id)
            if not task or task.get('status') != 'running':
                return False

            # 如果有正在运行的进程，终止它
            if task_id in self.running_processes:
                process = self.running_processes[task_id]
                try:
                    process.terminate()
                    # 等待进程结束，最多等待5秒
                    await asyncio.wait_for(process.wait(), timeout=5.0)
                except asyncio.TimeoutError:
                    # 如果进程没有响应terminate，强制杀死
                    process.kill()
                    await process.wait()
                finally:
                    # 清理进程记录
                    del self.running_processes[task_id]

            # 更新任务状态为失败（被手动停止）
            end_time = datetime.now()
            await ad_database.update_task_status(task_id, TaskStatus.FAILED, end_time)

            # 获取最新日志并更新
            logs_result = await ad_database.get_task_logs(task_id, 1, 1)
            if logs_result and logs_result.get('items'):
                latest_log = logs_result['items'][0]
                if latest_log['status'] == 'running':
                    await ad_database.update_task_log(
                        latest_log['id'],
                        TaskStatus.FAILED,
                        end_time,
                        latest_log.get('output', ''),
                        latest_log.get('error', '') + '\n任务被手动停止',
                        -1
                    )

            logger.info(f"任务 {task.get('name')} (ID: {task_id}) 已被手动停止")
            return True

        except Exception as e:
            logger.error(f"停止任务失败: {e}")
            return False

    async def _execute_task(self, task_data: Dict[str, Any], is_manual: bool = False):
        """执行任务"""
        task_id = task_data['id']
        task_name = task_data['name']
        script_path = task_data['script_path']

        # 记录任务开始
        start_time = datetime.now()
        log_id = await self._create_task_log(task_id, TaskStatus.RUNNING, start_time)
        
        try:
            # 更新任务状态为运行中
            await ad_database.update_task_status(task_id, TaskStatus.RUNNING, start_time)
            
            # 设置工作目录为后端目录
            backend_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

            # 处理脚本路径
            if os.path.isabs(script_path):
                # 绝对路径直接使用
                full_script_path = script_path
                work_dir = os.path.dirname(script_path) if os.path.dirname(script_path) else backend_dir
            else:
                # 相对路径基于后端目录
                full_script_path = os.path.join(backend_dir, script_path)
                work_dir = backend_dir

            # 检查脚本文件是否存在
            if not os.path.exists(full_script_path):
                raise FileNotFoundError(f"脚本文件不存在: {full_script_path}")

            logger.info(f"执行脚本: {full_script_path}")
            logger.info(f"工作目录: {work_dir}")

            # 准备环境变量
            env = os.environ.copy()
            if task_data.get('env_vars'):
                env.update(task_data['env_vars'])

            # 添加项目路径到PYTHONPATH
            if 'PYTHONPATH' in env:
                env['PYTHONPATH'] = f"{backend_dir}:{env['PYTHONPATH']}"
            else:
                env['PYTHONPATH'] = backend_dir

            # 执行脚本
            logger.info(f"开始执行任务: {task_name}")

            process = await asyncio.create_subprocess_exec(
                'python', '-u', full_script_path,  # -u 参数强制无缓冲输出
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                env=env,
                cwd=work_dir
            )

            # 记录正在运行的进程
            self.running_processes[task_id] = process
            
            # 实时读取脚本输出
            timeout = task_data.get('timeout')
            stdout_lines = []
            stderr_lines = []

            try:
                # 创建实时读取输出的任务
                async def read_output():
                    from ..config.settings import settings

                    last_update_time = time.time()
                    lines_since_update = 0
                    update_interval = settings.LOG_UPDATE_INTERVAL  # 从配置文件读取
                    batch_size = settings.LOG_BATCH_SIZE  # 从配置文件读取

                    # 从配置文件读取紧急关键词
                    urgent_keywords = settings.LOG_URGENT_KEYWORDS

                    # 统计信息
                    total_lines = 0
                    urgent_updates = 0
                    batch_updates = 0
                    time_updates = 0

                    async def should_update_now(line_content):
                        """判断是否需要立即更新"""
                        nonlocal last_update_time, lines_since_update, total_lines, urgent_updates, batch_updates, time_updates

                        total_lines += 1
                        current_time = time.time()
                        time_elapsed = current_time - last_update_time

                        # 检查是否包含紧急关键词
                        is_urgent = any(keyword in line_content for keyword in urgent_keywords)

                        # 判断更新原因
                        update_reason = None
                        if is_urgent:
                            update_reason = "urgent"
                            urgent_updates += 1
                        elif lines_since_update >= batch_size:
                            update_reason = "batch"
                            batch_updates += 1
                        elif time_elapsed >= update_interval:
                            update_reason = "time"
                            time_updates += 1

                        should_update = update_reason is not None

                        if should_update:
                            last_update_time = current_time
                            lines_since_update = 0
                            # 记录优化统计信息（每50次更新记录一次）
                            if (urgent_updates + batch_updates + time_updates) % 50 == 0:
                                logger.debug(f"日志优化统计 - 总行数:{total_lines}, 紧急更新:{urgent_updates}, 批量更新:{batch_updates}, 时间更新:{time_updates}")
                            return True

                        lines_since_update += 1
                        return False

                    while True:
                        # 检查进程是否结束
                        if process.returncode is not None:
                            # 进程结束时进行最后一次更新
                            if stdout_lines or stderr_lines:
                                current_output = '\n'.join(stdout_lines)
                                current_error = '\n'.join(stderr_lines)
                                await self._update_task_log_realtime(log_id, current_output, current_error)
                            break

                        has_new_content = False

                        try:
                            # 读取一行标准输出
                            line = await asyncio.wait_for(process.stdout.readline(), timeout=0.1)
                            if line:
                                decoded_line = line.decode('utf-8', errors='ignore').rstrip()
                                if decoded_line:
                                    stdout_lines.append(decoded_line)
                                    has_new_content = True

                                    # 判断是否需要立即更新
                                    if await should_update_now(decoded_line):
                                        current_output = '\n'.join(stdout_lines)
                                        current_error = '\n'.join(stderr_lines)
                                        await self._update_task_log_realtime(log_id, current_output, current_error)
                        except asyncio.TimeoutError:
                            pass

                        try:
                            # 读取一行错误输出
                            line = await asyncio.wait_for(process.stderr.readline(), timeout=0.1)
                            if line:
                                decoded_line = line.decode('utf-8', errors='ignore').rstrip()
                                if decoded_line:
                                    stderr_lines.append(decoded_line)
                                    has_new_content = True

                                    # 错误输出通常比较重要，立即更新
                                    current_output = '\n'.join(stdout_lines)
                                    current_error = '\n'.join(stderr_lines)
                                    await self._update_task_log_realtime(log_id, current_output, current_error)
                                    last_update_time = time.time()
                                    lines_since_update = 0
                        except asyncio.TimeoutError:
                            pass

                        # 如果没有新内容，适当增加休眠时间
                        if not has_new_content:
                            await asyncio.sleep(0.2)  # 无新内容时休眠更久
                        else:
                            await asyncio.sleep(0.05)  # 有新内容时快速检查

                # 启动读取任务
                read_task = asyncio.create_task(read_output())

                # 等待进程完成或超时
                try:
                    await asyncio.wait_for(process.wait(), timeout=timeout)
                except asyncio.TimeoutError:
                    process.kill()
                    raise Exception(f"任务执行超时 ({timeout}秒)")
                finally:
                    # 取消读取任务
                    read_task.cancel()
                    try:
                        await read_task
                    except asyncio.CancelledError:
                        pass

                # 读取剩余的输出
                remaining_stdout, remaining_stderr = await process.communicate()
                if remaining_stdout:
                    remaining_lines = remaining_stdout.decode('utf-8', errors='ignore').strip()
                    if remaining_lines:
                        for line in remaining_lines.split('\n'):
                            if line.strip():
                                stdout_lines.append(line.strip())

                if remaining_stderr:
                    remaining_lines = remaining_stderr.decode('utf-8', errors='ignore').strip()
                    if remaining_lines:
                        for line in remaining_lines.split('\n'):
                            if line.strip():
                                stderr_lines.append(line.strip())

            except Exception as e:
                if "超时" not in str(e):
                    logger.error(f"读取任务输出时发生错误: {e}")
                process.kill()
                await process.wait()
                raise e

            # 处理执行结果
            exit_code = process.returncode
            output = '\n'.join(stdout_lines)
            error = '\n'.join(stderr_lines)

            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()

            if exit_code == 0:
                status = TaskStatus.SUCCESS
                await ad_database.increment_task_success_count(task_id)
                logger.info(f"任务 {task_name} 执行成功，耗时 {duration:.2f}秒")
            else:
                status = TaskStatus.FAILED
                await ad_database.increment_task_fail_count(task_id)
                logger.error(f"任务 {task_name} 执行失败，退出码: {exit_code}")

            # 更新任务状态
            await ad_database.update_task_status(task_id, status, end_time)

            # 最终更新任务日志
            await self._update_task_log(log_id, status, end_time, output, error, exit_code or 0)

            # 更新下次运行时间
            await self._update_next_run_time(task_id)

            # 清理进程记录
            if task_id in self.running_processes:
                del self.running_processes[task_id]

        except Exception as e:
            end_time = datetime.now()
            error_msg = str(e)
            
            # 更新任务状态为失败
            await ad_database.update_task_status(task_id, TaskStatus.FAILED, end_time)
            await ad_database.increment_task_fail_count(task_id)
            
            # 更新任务日志
            await self._update_task_log(log_id, TaskStatus.FAILED, end_time, "", error_msg, -1)

            # 清理进程记录
            if task_id in self.running_processes:
                del self.running_processes[task_id]

            logger.error(f"任务 {task_name} 执行异常: {error_msg}")
    
    async def _create_task_log(self, task_id: int, status: TaskStatus, start_time: datetime) -> int:
        """创建任务日志"""
        return await ad_database.create_task_log(task_id, status, start_time)
    
    async def _update_task_log(self, log_id: int, status: TaskStatus, end_time: datetime,
                              output: str, error: str, exit_code: int):
        """更新任务日志"""
        await ad_database.update_task_log(log_id, status, end_time, output, error, exit_code)

    async def _update_task_log_realtime(self, log_id: int, output: str, error: str):
        """实时更新任务日志（只更新输出内容，不更新状态）"""
        try:
            await ad_database.update_task_log_output(log_id, output, error)
        except Exception as e:
            logger.error(f"实时更新任务日志失败: {e}")

    async def _update_next_run_time(self, task_id: int):
        """更新任务的下次运行时间"""
        try:
            job_id = f"task_{task_id}"
            job = self.scheduler.get_job(job_id)
            if job and job.next_run_time:
                next_run_time = job.next_run_time.strftime('%Y-%m-%d %H:%M:%S')
                await ad_database.update_task_next_run_time(task_id, next_run_time)
        except Exception as e:
            logger.error(f"更新任务下次运行时间失败: {e}")
    
    async def _load_tasks_from_db(self):
        """从数据库加载任务"""
        try:
            tasks = await ad_database.get_enabled_tasks()
            for task in tasks:
                await self.add_task(task)
            logger.info(f"从数据库加载了 {len(tasks)} 个任务")
        except Exception as e:
            logger.error(f"从数据库加载任务失败: {e}")
    
    def _job_executed(self, event):
        """任务执行完成事件"""
        logger.debug(f"任务执行完成: {event.job_id}")
    
    def _job_error(self, event):
        """任务执行错误事件"""
        logger.error(f"任务执行错误: {event.job_id}, 异常: {event.exception}")
    
    def get_running_jobs(self) -> List[Dict[str, Any]]:
        """获取正在运行的任务"""
        jobs = []
        for job in self.scheduler.get_jobs():
            jobs.append({
                'id': job.id,
                'name': job.name,
                'next_run_time': job.next_run_time.isoformat() if job.next_run_time else None,
                'trigger': str(job.trigger)
            })
        return jobs

# 全局任务调度器实例
task_scheduler = TaskScheduler()
