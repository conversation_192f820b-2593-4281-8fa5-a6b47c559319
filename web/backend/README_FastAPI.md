# FastAPI 后端项目

## 🚀 项目概述

本项目已从 Flask 迁移到 FastAPI，采用模块化架构设计，提供更好的性能、自动文档生成和类型安全。

## 📁 项目结构

```
backend/
├── main.py                 # FastAPI 主应用入口
├── start_fastapi.py       # 启动脚本
├── requirements.txt       # 项目依赖
├── database/             # 数据库文件
│   └── admin.db
└── src/                  # 源代码目录
    ├── config/           # 配置模块
    │   ├── __init__.py
    │   └── settings.py   # 应用配置
    ├── models/           # 数据模型
    │   ├── __init__.py
    │   ├── user.py       # 用户相关模型
    │   └── response.py   # 响应模型
    ├── routes/           # 路由模块
    │   ├── __init__.py
    │   ├── user.py       # 用户相关路由
    │   ├── admin.py      # 管理相关路由
    │   └── health.py     # 健康检查路由
    └── utils/            # 工具模块
        ├── __init__.py
        ├── database.py   # 数据库工具
        └── auth.py       # 认证工具
```

## 🛠️ 技术栈

- **FastAPI**: 现代、快速的 Web 框架
- **Uvicorn**: ASGI 服务器
- **Pydantic**: 数据验证和序列化
- **python-jose**: JWT 令牌处理
- **passlib**: 密码哈希
- **aiosqlite**: 异步 SQLite 数据库

## 🚀 启动方式

### 方式一：直接运行主文件
```bash
cd backend
python main.py
```

### 方式二：使用启动脚本
```bash
cd backend
python start_fastapi.py
```

### 方式三：使用 uvicorn 命令
```bash
cd backend
uvicorn main:app --host 0.0.0.0 --port 3000 --reload
```

## 📚 API 文档

FastAPI 自动生成交互式 API 文档：

- **Swagger UI**: http://localhost:3000/docs
- **ReDoc**: http://localhost:3000/redoc
- **OpenAPI JSON**: http://localhost:3000/openapi.json

## 🔧 主要功能

### 用户管理
- `POST /api/user/login` - 用户登录
- `GET /api/user/info` - 获取用户信息
- `POST /api/user/logout` - 用户登出

### Vue Element Admin 接口
- `GET /api/vue-element-admin/transaction/list` - 交易列表
- `GET /api/vue-element-admin/search/user` - 用户搜索
- `GET /api/vue-element-admin/article/list` - 文章列表
- `GET /api/vue-element-admin/roles` - 角色列表

### 系统接口
- `GET /api/health` - 健康检查
- `GET /` - 根路径信息

## 🔐 认证方式

使用 JWT Bearer Token 认证：

```bash
# 登录获取 token
curl -X POST http://localhost:3000/api/user/login \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "123456"}'

# 使用 token 访问受保护的接口
curl -X GET http://localhost:3000/api/user/info \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

## 👥 默认账户

- **管理员**: `admin` / `123456`
- **编辑员**: `editor` / `123456`

## ✨ FastAPI 优势

1. **自动文档生成**: 无需手动维护 API 文档
2. **类型安全**: 基于 Python 类型提示
3. **数据验证**: 自动请求/响应验证
4. **高性能**: 基于 Starlette 和 Pydantic
5. **现代化**: 原生支持异步编程
6. **易于测试**: 内置测试客户端

## 🔄 从 Flask 迁移的改进

1. **模块化结构**: 清晰的代码组织
2. **自动中文支持**: 无需额外配置
3. **更好的错误处理**: 统一的异常处理机制
4. **类型提示**: 更好的 IDE 支持和代码提示
5. **自动文档**: 实时更新的 API 文档
