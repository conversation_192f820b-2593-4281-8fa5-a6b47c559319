# 青龙式任务调度管理系统

基于 APScheduler + FastAPI + Vue Element Admin 实现的任务调度管理系统

## 🚀 快速启动

### 1. 启动后端服务
```bash
cd web/backend
python -m uvicorn main:app --host 0.0.0.0 --port 3000 --reload
```

### 2. 启动前端服务
```bash
cd web/frontend
npm run dev
```

### 3. 访问系统
- **前端地址**: http://localhost:9527
- **任务管理**: http://localhost:9527/#/tasks/dashboard
- **后端API**: http://localhost:3000/docs (Swagger文档)

### 4. 系统状态检查
```bash
# 检查后端服务
curl http://localhost:3000/health

# 检查任务列表
curl http://localhost:3000/api/vue-element-admin/tasks
```

## 🚀 功能特性

### 后端功能
- ✅ **APScheduler 任务调度** - 支持 Cron、间隔、一次性任务
- ✅ **任务管理** - 创建、编辑、删除、启用/禁用任务
- ✅ **立即执行** - `/run_now` 接口支持手动触发任务
- ✅ **任务日志** - 完整的执行日志记录，包括输出、错误、执行时长
- ✅ **环境变量** - 支持为任务设置自定义环境变量
- ✅ **超时控制** - 任务执行超时保护
- ✅ **并发控制** - 最大实例数限制
- ✅ **数据持久化** - SQLite 数据库存储任务配置和日志
- ✅ **投放自动化** - 集成投放数据筛选和处理脚本

### 前端功能
- ✅ **任务概览** - 统计面板，快速查看任务状态和下次运行时间
- ✅ **任务列表** - 分页显示，搜索过滤，调度配置展示
- ✅ **任务创建/编辑** - 可视化表单配置任务，支持弹窗编辑
- ✅ **任务日志** - 详细的执行日志查看，支持实时刷新
- ✅ **立即执行** - 一键执行任务，支持临时环境变量
- ✅ **状态切换** - 启用/禁用任务开关，实时状态更新
- ✅ **调度配置显示** - 直观显示Cron表达式、间隔时间等
- ✅ **下次运行时间** - 智能颜色标识，即将执行/逾期提醒

## 📁 项目结构

```
web/
├── backend/
│   ├── src/
│   │   ├── models/task.py          # 任务数据模型
│   │   ├── services/
│   │   │   ├── task_scheduler.py   # 任务调度器服务
│   │   │   └── data_fetcher.py     # 数据获取服务
│   │   ├── routes/
│   │   │   ├── tasks.py           # 任务管理API路由
│   │   │   └── admin.py           # 管理功能API
│   │   └── utils/ad_database.py    # 统一数据库管理
│   ├── scripts/                    # 脚本目录
│   │   ├── hello_world.py         # Hello World示例
│   │   ├── data_backup.py         # 数据备份示例
│   │   └── 投放自动化/             # 投放自动化脚本
│   │       ├── 合并脚本.py         # 完整版合并脚本
│   │       ├── 合并脚本_简化版.py   # 简化版合并脚本
│   │       └── README.md          # 投放自动化说明
│   ├── database/                   # 数据库文件
│   │   └── ad_platform.db         # SQLite数据库
│   └── main.py                    # 应用入口
└── frontend/
    ├── src/
    │   ├── api/tasks.js           # 任务管理API接口
    │   ├── router/modules/tasks.js # 任务管理路由
    │   └── views/tasks/           # 任务管理页面
    │       ├── dashboard.vue      # 任务概览（统计面板）
    │       ├── list.vue          # 任务列表（支持弹窗编辑）
    │       ├── create.vue        # 创建任务
    │       ├── edit.vue          # 编辑任务（独立页面）
    │       └── logs.vue          # 任务日志
    └── ...
```

## 🛠️ 技术栈

### 后端
- **FastAPI** - 现代化的 Python Web 框架
- **APScheduler** - 高级 Python 调度器
- **SQLite** - 轻量级数据库
- **Pydantic** - 数据验证和序列化
- **asyncio** - 异步编程支持

### 前端
- **Vue.js 2** - 渐进式 JavaScript 框架
- **Element UI** - Vue.js 2.0 桌面端组件库
- **Vue Router** - Vue.js 官方路由管理器
- **Axios** - HTTP 客户端

## 📋 API 接口

### 任务管理
- `GET /api/vue-element-admin/tasks` - 获取任务列表
- `POST /api/vue-element-admin/tasks` - 创建任务
- `GET /api/vue-element-admin/tasks/{id}` - 获取任务详情
- `PUT /api/vue-element-admin/tasks/{id}` - 更新任务
- `DELETE /api/vue-element-admin/tasks/{id}` - 删除任务

### 任务控制
- `POST /api/vue-element-admin/tasks/{id}/run_now` - 立即执行任务
- `POST /api/vue-element-admin/tasks/{id}/pause` - 暂停任务
- `POST /api/vue-element-admin/tasks/{id}/resume` - 恢复任务

### 任务日志
- `GET /api/vue-element-admin/tasks/{id}/logs` - 获取任务执行日志
- `GET /api/vue-element-admin/tasks/stats` - 获取任务统计

## 🎯 任务类型

### 1. Cron 定时任务
```json
{
  "task_type": "cron",
  "cron_expression": "0 2 * * *"  // 每天凌晨2点
}
```

### 2. 间隔任务
```json
{
  "task_type": "interval",
  "interval_seconds": 300  // 每5分钟
}
```

### 3. 一次性任务
```json
{
  "task_type": "date",
  "run_date": "2025-07-02T10:00:00"
}
```

## 📝 示例脚本

### 1. Hello World 脚本
```python
#!/usr/bin/env python3
import os
from datetime import datetime

def main():
    print(f"[{datetime.now()}] Hello World!")
    print(f"当前用户: {os.environ.get('USER', 'unknown')}")
    print("脚本执行完成")

if __name__ == "__main__":
    main()
```

### 2. 数据备份脚本
```python
#!/usr/bin/env python3
import os
import json
from datetime import datetime

def main():
    backup_type = os.environ.get('BACKUP_TYPE', 'full')
    backup_dir = os.environ.get('BACKUP_DIR', '/tmp/backups')
    
    # 执行备份逻辑
    print(f"开始执行 {backup_type} 备份到 {backup_dir}")
    # ... 备份代码 ...
    print("备份完成")

if __name__ == "__main__":
    main()
```

## 🎯 当前运行状态

系统已成功创建并运行了以下任务：

### 示例任务
1. **Hello World任务** ✅
   - 类型: Cron (`*/2 * * * *` - 每2分钟执行)
   - 状态: 正常运行，成功率100%
   - 功能: 展示基本的任务执行功能

2. **数据备份任务** ✅
   - 类型: Cron (`0 2 * * *` - 每天凌晨2点)
   - 状态: 正常运行
   - 功能: 演示复杂任务和环境变量使用

3. **投放自动化合并脚本** ✅
   - 类型: Cron (`0 */6 * * *` - 每6小时执行)
   - 状态: 正常运行
   - 功能: 账户筛选、创意筛选、图片处理
   - 数据: 成功筛选出6个高花费账户（>=800元）

### 投放自动化成果
- **游戏数据**: 891条记录
- **账户数据**: 4434条记录
- **筛选结果**: 找到6个符合条件的高花费账户
  - 40297242: 7332.10元 (经典救救狗头小游戏)
  - 46273208: 4669.80元 (经典救救狗头小游戏)
  - 52175929: 2387.21元 (救救狗狗大作战游戏)
  - 41323350: 1199.99元 (狗头画线大作战)
  - 41634551: 1154.51元 (画线救狗头游戏)
  - 还有更多...

## 📊 系统监控

### 任务状态
- **pending** - 等待中 (灰色)
- **running** - 运行中 (黄色)
- **success** - 成功 (绿色)
- **failed** - 失败 (红色)
- **paused** - 暂停 (灰色)

### 下次运行时间状态
- 🔵 **正常** (蓝色) - 30分钟后执行
- 🟡 **即将执行** (黄色加粗) - 30分钟内执行
- 🔴 **逾期** (红色加粗) - 已过期但未执行
- ⚪ **禁用** (灰色) - 任务已禁用

### 执行统计
- 总执行次数
- 成功次数
- 失败次数
- 成功率
- 平均执行时长
- 下次运行时间

## 🔧 配置说明

### 任务配置参数
- **name** - 任务名称（必填）
- **description** - 任务描述
- **script_path** - Python脚本路径（必填）
- **task_type** - 任务类型：cron/interval/date
- **cron_expression** - Cron表达式（Cron任务必填）
- **interval_seconds** - 间隔秒数（间隔任务必填）
- **run_date** - 执行时间（一次性任务必填）
- **enabled** - 是否启用
- **max_instances** - 最大并发实例数
- **timeout** - 超时时间（秒）
- **env_vars** - 环境变量（JSON对象）

## 🎉 功能演示

### 核心功能
所有任务都支持：
- ✅ **立即执行** - 一键手动触发，支持临时环境变量
- ✅ **启用/禁用** - 实时切换任务状态
- ✅ **弹窗编辑** - 无需跳转页面，快速修改配置
- ✅ **详细日志** - 完整的执行记录，包括输出、错误、时长
- ✅ **环境变量** - 动态传递配置参数
- ✅ **调度配置** - 直观显示Cron表达式、间隔时间
- ✅ **下次运行** - 智能提醒即将执行的任务

### 投放自动化集成
- ✅ **数据库统一** - 使用统一的AdDatabase系统
- ✅ **异步处理** - 完全异步化，性能优异
- ✅ **智能筛选** - 根据版本、名称、花费等条件筛选
- ✅ **实时监控** - 通过任务调度系统监控执行状态
- ✅ **错误处理** - 完善的异常处理和重试机制

## 🔮 未来扩展

### 计划功能
- [ ] **任务依赖关系** - 支持任务间的依赖执行
- [ ] **任务分组管理** - 按业务模块分组管理任务
- [ ] **通知系统** - 邮件/钉钉/企业微信通知
- [ ] **执行图表** - 任务执行历史和性能图表
- [ ] **分布式调度** - 支持多节点分布式任务调度
- [ ] **任务模板** - 预定义任务模板，快速创建
- [ ] **资源监控** - CPU、内存使用情况监控
- [ ] **任务链** - 复杂工作流的任务链执行

### 投放自动化扩展
- [ ] **创意筛选完善** - 完整的ROI筛选逻辑
- [ ] **图片处理优化** - 批量图片下载和处理
- [ ] **数据分析** - 投放效果分析和报表
- [ ] **自动优化** - 基于历史数据的自动优化建议
- [ ] **多平台支持** - 支持更多广告平台

## 📞 技术支持

如有问题或建议，请：
1. 查看日志文件了解详细错误信息
2. 检查任务配置是否正确
3. 确认脚本路径和权限
4. 验证环境变量设置

**系统已稳定运行，投放自动化功能正常工作！** 🎉
