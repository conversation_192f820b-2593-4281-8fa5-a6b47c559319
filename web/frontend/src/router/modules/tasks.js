/** When your routing table is too long, you can split it into small modules **/

import Layout from '@/layout'

const tasksRouter = {
  path: '/tasks',
  component: Layout,
  redirect: '/tasks/list',
  name: 'Tasks',
  meta: {
    title: '任务管理',
    icon: 'el-icon-timer',
    roles: ['admin', 'editor']
  },
  children: [
    {
      path: 'list',
      component: () => import('@/views/tasks/list'),
      name: 'TaskList',
      meta: {
        title: '任务列表',
        icon: 'list'
      }
    },
    {
      path: 'config',
      component: () => import('@/views/tasks/config'),
      name: 'TaskConfig',
      meta: {
        title: '任务配置',
        icon: 'el-icon-setting'
      }
    }
  ]
}

export default tasksRouter
