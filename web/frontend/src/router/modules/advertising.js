/** When your routing table is too long, you can split it into small modules **/

import Layout from '@/layout'

const advertisingRouter = {
  path: '/advertising',
  component: Layout,
  redirect: '/advertising/campaign-data',
  name: 'Advertising',
  meta: {
    title: '投放自动化',
    icon: 'el-icon-s-marketing',
    roles: ['admin', 'editor'] // 可以根据需要调整权限
  },
  children: [
    {
      path: 'campaign-data',
      component: () => import('@/views/advertising/campaign-data/index'),
      name: 'CampaignData',
      meta: {
        title: '投放数据',
        icon: 'el-icon-data-line'
      }
    },
    {
      path: 'game-data',
      component: () => import('@/views/advertising/game-data/index'),
      name: 'GameData',
      meta: {
        title: '小游戏数据',
        icon: 'el-icon-trophy'
      }
    },
    {
      path: 'config-management',
      component: () => import('@/views/advertising/config-management/index'),
      name: 'ConfigManagement',
      meta: {
        title: '配置管理',
        icon: 'el-icon-setting'
      }
    }
  ]
}

export default advertisingRouter
