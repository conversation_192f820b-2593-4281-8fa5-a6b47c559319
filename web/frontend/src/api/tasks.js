import request from '@/utils/request'

// 获取任务列表
export function getTaskList(query) {
  return request({
    url: '/vue-element-admin/tasks',
    method: 'get',
    params: query
  })
}

// 获取任务详情
export function getTask(id) {
  return request({
    url: `/vue-element-admin/tasks/${id}`,
    method: 'get'
  })
}

// 创建任务
export function createTask(data) {
  return request({
    url: '/vue-element-admin/tasks',
    method: 'post',
    data
  })
}

// 更新任务
export function updateTask(id, data) {
  return request({
    url: `/vue-element-admin/tasks/${id}`,
    method: 'put',
    data
  })
}

// 删除任务
export function deleteTask(id) {
  return request({
    url: `/vue-element-admin/tasks/${id}`,
    method: 'delete'
  })
}

// 立即执行任务
export function runTaskNow(id, data = {}) {
  return request({
    url: `/vue-element-admin/tasks/${id}/run_now`,
    method: 'post',
    data
  })
}

// 暂停任务
export function pauseTask(id) {
  return request({
    url: `/vue-element-admin/tasks/${id}/pause`,
    method: 'post'
  })
}

// 恢复任务
export function resumeTask(id) {
  return request({
    url: `/vue-element-admin/tasks/${id}/resume`,
    method: 'post'
  })
}

// 获取任务日志
export function getTaskLogs(id, query) {
  return request({
    url: `/vue-element-admin/tasks/${id}/logs`,
    method: 'get',
    params: query
  })
}

// 获取任务统计
export function getTaskStats() {
  return request({
    url: '/vue-element-admin/tasks/stats',
    method: 'get'
  })
}

// 获取任务最新日志
export function getLatestTaskLog(id) {
  return request({
    url: `/vue-element-admin/tasks/${id}/logs/latest`,
    method: 'get'
  })
}

// 获取任务运行状态
export function getTaskStatus(id) {
  return request({
    url: `/vue-element-admin/tasks/${id}/status`,
    method: 'get'
  })
}

// ==================== 任务视图管理 ====================

// 获取任务视图列表
export function getTaskViews() {
  return request({
    url: '/vue-element-admin/tasks/views',
    method: 'get'
  })
}

// 创建任务视图
export function createTaskView(data) {
  return request({
    url: '/vue-element-admin/tasks/views',
    method: 'post',
    data
  })
}

// 更新任务视图
export function updateTaskView(id, data) {
  return request({
    url: `/vue-element-admin/tasks/views/${id}`,
    method: 'put',
    data
  })
}

// 删除任务视图
export function deleteTaskView(id) {
  return request({
    url: `/vue-element-admin/tasks/views/${id}`,
    method: 'delete'
  })
}

// 根据视图获取任务列表
export function getTasksByView(viewId, params) {
  return request({
    url: `/vue-element-admin/tasks/views/${viewId}/tasks`,
    method: 'get',
    params
  })
}
