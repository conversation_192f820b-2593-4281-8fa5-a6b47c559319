<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.search"
        placeholder="搜索筛选名称或筛选版本"
        style="width: 250px;"
        class="filter-item"
        @keyup.enter.native="handleFilter"
      />
      <el-button
        v-waves
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleFilter"
      >
        搜索
      </el-button>
      <el-button
        class="filter-item"
        style="margin-left: 10px;"
        type="primary"
        icon="el-icon-plus"
        @click="handleCreate"
      >
        新增配置
      </el-button>

      <el-dropdown
        class="filter-item"
        style="margin-left: 10px;"
        :disabled="multipleSelection.length === 0"
        @command="handleBatchOperation"
        trigger="click"
      >
        <el-button
          :type="multipleSelection.length > 0 ? 'warning' : 'info'"
          :disabled="multipleSelection.length === 0 || batchLoading"
          :loading="batchLoading"
          size="small"
        >
          <i class="el-icon-s-operation"></i>
          批操作
          <span v-if="multipleSelection.length > 0">({{ multipleSelection.length }})</span>
          <i class="el-icon-arrow-down el-icon--right"></i>
        </el-button>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item command="batchEnable">
            <i class="el-icon-check" style="color: #67C23A;"></i> 批量启用
          </el-dropdown-item>
          <el-dropdown-item command="batchDisable">
            <i class="el-icon-close" style="color: #F56C6C;"></i> 批量禁用
          </el-dropdown-item>
          <el-dropdown-item command="batchDelete" divided>
            <i class="el-icon-delete" style="color: #F56C6C;"></i> 批量删除
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>

    <el-table
      :key="tableKey"
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%;"
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        type="selection"
        width="55"
        align="center"
      />
      <el-table-column label="配置组名称" min-width="180px">
        <template slot-scope="{row}">
          <div style="display: flex; align-items: center;">
            <el-tag
              :type="row.enabled ? 'success' : 'danger'"
              size="mini"
              style="margin-right: 8px;"
            >
              {{ row.enabled ? '启用' : '禁用' }}
            </el-tag>
            <span class="config-name">{{ row.group_name }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="appid" width="200px" align="center">
        <template slot-scope="{row}">
          <span
            class="app-id clickable"
            @click="copyAppId(row.appId)"
          >
            {{ row.appId }}
          </span>
        </template>
      </el-table-column>

      <el-table-column label="标签" min-width="180px">
        <template slot-scope="{row}">
          <el-tag
            v-for="label in row.labelName"
            :key="label"
            size="mini"
            type="primary"
            style="margin-right: 5px; margin-bottom: 2px;"
          >
            {{ label }}
          </el-tag>
          <span v-if="!row.labelName || row.labelName.length === 0" class="text-muted">无</span>
        </template>
      </el-table-column>

      <el-table-column label="操作" align="center" width="280" class-name="small-padding fixed-width">
        <template slot-scope="{row,$index}">
          <div style="font-size: xx-small;">
            <el-button type="text" size="mini" @click="handleEdit(row)">
              <i class="el-icon-edit"></i>
              <span>编辑</span>
            </el-button>
            <el-button type="text" size="mini" @click="handleCopy(row)">
              <i class="el-icon-copy-document"></i>
              <span>复制</span>
            </el-button>
            <el-button
              type="text"
              size="mini"
              @click="handleToggleStatus(row)"
            >
              <i :class="row.enabled ? 'el-icon-close' : 'el-icon-check'"></i>
              <span>{{ row.enabled ? '禁用' : '启用' }}</span>
            </el-button>
            <el-button type="text" size="mini" @click="handleDelete(row,$index)">
              <i class="el-icon-delete"></i>
              <span>删除</span>
            </el-button>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="筛选版本" min-width="180px">
        <template slot-scope="{row}">
          <el-tag
            v-for="version in row.filter_version"
            :key="version"
            size="mini"
            type="info"
            style="margin-right: 5px; margin-bottom: 2px;"
          >
            {{ version }}
          </el-tag>
          <span v-if="!row.filter_version || row.filter_version.length === 0" class="text-muted">无</span>
        </template>
      </el-table-column>
      <el-table-column label="筛选名称" min-width="180px">
        <template slot-scope="{row}">
          <el-tag
            v-for="name in row.filter_name"
            :key="name"
            size="mini"
            style="margin-right: 5px; margin-bottom: 2px;"
          >
            {{ name }}
          </el-tag>
          <span v-if="!row.filter_name || row.filter_name.length === 0" class="text-muted">无</span>
        </template>
      </el-table-column>
      <el-table-column label="最低花费" width="100px" align="center">
        <template slot-scope="{row}">
          <span class="cost-text">{{ row.min_cost }}</span>
        </template>
      </el-table-column>
      <el-table-column label="最低ROI" width="100px" align="center">
        <template slot-scope="{row}">
          <span class="roi-text">{{ row.min_MonetizationRoi }}</span>
        </template>
      </el-table-column>

      <el-table-column label="uids" min-width="200px">
        <template slot-scope="{row}">
          <div v-if="row.uids && row.uids.length > 0" class="uids-display">
            <el-tag
              v-for="(uid, index) in row.uids.slice(0, 3)"
              :key="index"
              size="mini"
              type="info"
              style="margin: 1px;"
            >
              {{ uid }}
            </el-tag>
            <span v-if="row.uids.length > 3" class="more-uids">
              +{{ row.uids.length - 3 }}个
            </span>
          </div>
          <span v-else class="text-muted">无</span>
        </template>
      </el-table-column>

      <el-table-column label="更新时间" width="160px" align="center">
        <template slot-scope="{row}">
          <span>{{ formatTime(row.updated_at) }}</span>
        </template>
      </el-table-column>

    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.limit"
      @pagination="getList"
    />

    <!-- 新增/编辑配置对话框 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogFormVisible" width="600px">
      <el-form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="left"
        label-width="120px"
        style="width: 500px; margin-left:50px;"
      >
        <el-form-item label="配置组名称">
          <el-input
            v-model="temp.group_name"
            placeholder="留空则自动生成（如：config_1）"
            :disabled="dialogStatus === 'update'"
          />
          <div style="font-size: 12px; color: #909399; margin-top: 4px;">
            可选项，不填写将自动生成唯一名称
          </div>
        </el-form-item>

        <el-form-item label="appid" prop="appId">
          <el-input v-model="temp.appId" placeholder="请输入appid（必填）" />
        </el-form-item>

        <el-form-item label="标签" prop="labelName">
          <el-input
            v-model="labelNameInput"
            type="textarea"
            :rows="3"
            placeholder="请输入标签，每行一个或用回车分隔（必填）"
            style="width: 100%;"
            @input="handleLabelNameInput"
          />
          <div class="form-tip">
            支持多种格式：每行一个标签，或用回车、逗号、分号分隔
          </div>
          <div v-if="temp.labelName && temp.labelName.length > 0" class="tags-preview">
            <el-tag
              v-for="(label, index) in temp.labelName"
              :key="index"
              closable
              size="mini"
              type="primary"
              @close="removeLabelNameTag(index)"
              style="margin: 2px;"
            >
              {{ label }}
            </el-tag>
          </div>
        </el-form-item>

        <el-form-item label="uids" prop="uids">
          <el-input
            v-model="uidsInput"
            type="textarea"
            :rows="3"
            placeholder="请输入uids，每行一个或用回车分隔（必填）"
            style="width: 100%;"
            @input="handleUidsInput"
          />
          <div class="form-tip">
            支持多种格式：每行一个uid，或用回车、逗号、分号分隔
          </div>
          <div v-if="temp.uids && temp.uids.length > 0" class="uids-preview">
            <el-tag
              v-for="(uid, index) in temp.uids"
              :key="index"
              closable
              size="mini"
              @close="removeUid(index)"
              style="margin: 2px;"
            >
              {{ uid }}
            </el-tag>
          </div>
        </el-form-item>

        <el-form-item label="筛选版本">
          <el-input
            v-model="versionInput"
            type="textarea"
            :rows="3"
            placeholder="请输入筛选版本，每行一个或用回车分隔"
            style="width: 100%;"
            @input="handleVersionInput"
          />
          <div class="form-tip">
            支持多种格式：每行一个版本，或用回车、逗号、分号分隔
          </div>
          <div v-if="temp.filter_version && temp.filter_version.length > 0" class="tags-preview">
            <el-tag
              v-for="(version, index) in temp.filter_version"
              :key="index"
              closable
              size="mini"
              @close="removeVersionTag(index)"
              style="margin: 2px;"
            >
              {{ version }}
            </el-tag>
          </div>
        </el-form-item>
        <el-form-item label="筛选名称">
          <el-input
            v-model="nameInput"
            type="textarea"
            :rows="3"
            placeholder="请输入筛选名称，每行一个或用回车分隔"
            style="width: 100%;"
            @input="handleNameInput"
          />
          <div class="form-tip">
            支持多种格式：每行一个名称，或用回车、逗号、分号分隔
          </div>
          <div v-if="temp.filter_name && temp.filter_name.length > 0" class="tags-preview">
            <el-tag
              v-for="(name, index) in temp.filter_name"
              :key="index"
              closable
              size="mini"
              @close="removeNameTag(index)"
              style="margin: 2px;"
            >
              {{ name }}
            </el-tag>
          </div>
        </el-form-item>
        <el-form-item label="最低花费" prop="min_cost">
          <el-input-number
            v-model="temp.min_cost"
            :min="1"
            :max="1000"
            :precision="0"
            placeholder="请输入最低花费"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="最低ROI" prop="min_MonetizationRoi">
          <el-input-number
            v-model="temp.min_MonetizationRoi"
            :min="0.1"
            :max="1"
            :precision="2"
            :step="0.01"
            placeholder="请输入最低ROI"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="启用状态">
          <el-switch v-model="temp.enabled" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">
          取消
        </el-button>
        <el-button type="primary" @click="dialogStatus==='create'?createData():updateData()">
          确认
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import waves from '@/directive/waves'
import Pagination from '@/components/Pagination'

export default {
  name: 'ConfigManagement',
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      tableKey: 0,
      list: [],
      total: 0,
      listLoading: true,
      multipleSelection: [],
      batchLoading: false,
      listQuery: {
        page: 1,
        limit: 20,
        search: undefined
      },
      temp: {
        id: undefined,
        group_name: '',
        filter_version: [],
        filter_name: [],
        min_cost: 1,
        min_MonetizationRoi: 0.1,
        appId: '',
        uids: [],
        enabled: true
      },
      dialogFormVisible: false,
      dialogStatus: '',
      rules: {
        appId: [{ required: true, message: 'appid是必填项', trigger: 'blur' }],
        labelName: [
          { required: true, message: '标签是必填项', trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              if (!value || value.length === 0) {
                callback(new Error('请至少输入一个标签'))
              } else {
                callback()
              }
            },
            trigger: 'blur'
          }
        ],
        min_cost: [{ required: true, message: '最低花费是必填项', trigger: 'blur' }],
        min_MonetizationRoi: [{ required: true, message: '最低ROI是必填项', trigger: 'blur' }],
        uids: [
          { required: true, message: 'uids是必填项', trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              if (!value || value.length === 0) {
                callback(new Error('请至少输入一个uid'))
              } else {
                callback()
              }
            },
            trigger: 'blur'
          }
        ]
      },
      // 输入框数据
      versionInput: '',
      nameInput: '',
      uidsInput: '',
      labelNameInput: ''
    }
  },
  computed: {
    dialogTitle() {
      return this.dialogStatus === 'create' ? '新增配置' : '编辑配置'
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.listLoading = true

      // 构建查询参数
      const params = {
        page: this.listQuery.page,
        limit: this.listQuery.limit
      }

      if (this.listQuery.search) {
        params.search = this.listQuery.search
      }

      // 调用真实API
      this.$request({
        url: '/vue-element-admin/filter-configs',
        method: 'get',
        params: params
      }).then(response => {
        if (response.code === 20000) {
          this.list = response.data.items || []
          this.total = response.data.total || 0
        } else {
          this.$message.error(response.message || '获取配置失败')
        }
        this.listLoading = false
      }).catch(error => {
        console.error('获取筛选配置失败:', error)
        this.$message.error('获取配置失败')
        this.listLoading = false
      })
    },
    formatTime(timeStr) {
      if (!timeStr) return '-'
      return new Date(timeStr).toLocaleString()
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleEdit(row) {
      this.temp = Object.assign({}, row)
      // 确保数组字段存在
      if (!this.temp.uids) this.temp.uids = []
      if (!this.temp.filter_version) this.temp.filter_version = []
      if (!this.temp.filter_name) this.temp.filter_name = []
      if (!this.temp.labelName) this.temp.labelName = []

      // 同步输入框显示
      this.uidsInput = this.temp.uids.join('\n')
      this.versionInput = this.temp.filter_version.join('\n')
      this.nameInput = this.temp.filter_name.join('\n')
      this.labelNameInput = this.temp.labelName.join('\n')

      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleCopy(row) {
      // 复制配置并打开编辑弹窗
      this.temp = {
        id: undefined,
        group_name: `${row.group_name}_副本`,
        filter_version: [...(row.filter_version || [])],
        filter_name: [...(row.filter_name || [])],
        min_cost: row.min_cost,
        min_MonetizationRoi: row.min_MonetizationRoi,
        appId: row.appId,
        labelName: [...(row.labelName || [])],
        uids: [...(row.uids || [])],
        enabled: true // 默认启用复制的配置
      }

      // 同步输入框显示
      this.uidsInput = this.temp.uids.join('\n')
      this.versionInput = this.temp.filter_version.join('\n')
      this.nameInput = this.temp.filter_name.join('\n')
      this.labelNameInput = this.temp.labelName.join('\n')

      // 设置为创建模式
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleToggleStatus(row) {
      const newStatus = !row.enabled
      const statusText = newStatus ? '启用' : '禁用'

      // 直接执行切换，不需要确认提醒
      this.$request({
        url: `/vue-element-admin/filter-configs/${row.id}`,
        method: 'put',
        data: { ...row, enabled: newStatus }
      }).then(response => {
        if (response.code === 20000) {
          row.enabled = newStatus
          this.$message.success(`${statusText}成功`)
        } else {
          this.$message.error(response.message || `${statusText}失败`)
        }
      }).catch(error => {
        console.error(`${statusText}配置失败:`, error)
        this.$message.error(`${statusText}失败`)
      })
    },
    async copyAppId(appId) {
      try {
        // 优先使用现代的Clipboard API
        if (navigator.clipboard && window.isSecureContext) {
          await navigator.clipboard.writeText(appId)
          this.$message.success(`AppID已复制: ${appId}`)
        } else {
          // 降级到传统方法
          const textArea = document.createElement('textarea')
          textArea.value = appId
          textArea.style.position = 'fixed'
          textArea.style.left = '-999999px'
          textArea.style.top = '-999999px'
          document.body.appendChild(textArea)
          textArea.focus()
          textArea.select()

          document.execCommand('copy')
          document.body.removeChild(textArea)
          this.$message.success(`AppID已复制: ${appId}`)
        }
      } catch (err) {
        console.error('复制失败:', err)
        this.$message.error('复制失败，请手动复制')
      }
    },
    handleDelete(row, index) {
      this.$confirm('确认删除该配置组?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$request({
          url: `/vue-element-admin/filter-configs/${row.id}`,
          method: 'delete'
        }).then(response => {
          if (response.code === 20000) {
            this.list.splice(index, 1)
            this.total--
            this.$message.success('删除成功!')
          } else {
            this.$message.error(response.message || '删除失败')
          }
        }).catch(error => {
          console.error('删除配置失败:', error)
          this.$message.error('删除失败')
        })
      })
    },
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.$request({
            url: '/vue-element-admin/filter-configs',
            method: 'post',
            data: this.temp
          }).then(response => {
            if (response.code === 20000) {
              this.dialogFormVisible = false
              this.$message.success('创建成功')
              this.getList() // 重新获取列表
            } else {
              this.$message.error(response.message || '创建失败')
            }
          }).catch(error => {
            console.error('创建配置失败:', error)
            this.$message.error('创建失败')
          })
        }
      })
    },
    updateData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.$request({
            url: `/vue-element-admin/filter-configs/${this.temp.id}`,
            method: 'put',
            data: this.temp
          }).then(response => {
            if (response.code === 20000) {
              this.dialogFormVisible = false
              this.$message.success('更新成功')
              this.getList() // 重新获取列表
            } else {
              this.$message.error(response.message || '更新失败')
            }
          }).catch(error => {
            console.error('更新配置失败:', error)
            this.$message.error('更新失败')
          })
        }
      })
    },
    resetTemp() {
      this.temp = {
        id: undefined,
        group_name: '',
        filter_version: [],
        filter_name: [],
        min_cost: 1,
        min_MonetizationRoi: 0.1,
        appId: '',
        labelName: [],
        uids: [],
        enabled: true
      }
      // 重置输入框状态
      this.versionInput = ''
      this.nameInput = ''
      this.uidsInput = ''
      this.labelNameInput = ''
    },

    // ==================== 输入处理相关方法 ====================

    // 筛选版本处理方法
    handleVersionInput(value) {
      const versions = value
        .split(/[\n,;，；\s]+/)
        .map(version => version.trim())
        .filter(version => version.length > 0)
      this.temp.filter_version = [...new Set(versions)]
    },

    removeVersionTag(index) {
      this.temp.filter_version.splice(index, 1)
      this.versionInput = this.temp.filter_version.join('\n')
    },

    // 筛选名称处理方法
    handleNameInput(value) {
      const names = value
        .split(/[\n,;，；\s]+/)
        .map(name => name.trim())
        .filter(name => name.length > 0)
      this.temp.filter_name = [...new Set(names)]
    },

    removeNameTag(index) {
      this.temp.filter_name.splice(index, 1)
      this.nameInput = this.temp.filter_name.join('\n')
    },

    // uids处理方法
    handleUidsInput(value) {
      // 解析输入的uids，支持多种分隔符
      const uids = value
        .split(/[\n,;，；\s]+/) // 支持换行、逗号、分号、空格分隔
        .map(uid => uid.trim())
        .filter(uid => uid.length > 0) // 过滤空字符串

      this.temp.uids = [...new Set(uids)] // 去重
    },

    removeUid(index) {
      this.temp.uids.splice(index, 1)
      // 更新输入框显示
      this.uidsInput = this.temp.uids.join('\n')
    },

    // 标签处理方法
    handleLabelNameInput(value) {
      const labels = value
        .split(/[\n,;，；\s]+/) // 支持换行、逗号、分号、空格分隔
        .map(label => label.trim())
        .filter(label => label.length > 0) // 过滤空字符串

      this.temp.labelName = [...new Set(labels)] // 去重
    },

    removeLabelNameTag(index) {
      this.temp.labelName.splice(index, 1)
      // 更新输入框显示
      this.labelNameInput = this.temp.labelName.join('\n')
    },

    // 多选相关方法
    handleSelectionChange(selection) {
      this.multipleSelection = selection
    },

    // 批操作处理
    handleBatchOperation(command) {
      if (this.multipleSelection.length === 0) {
        this.$message.warning('请先选择要操作的配置')
        return
      }

      switch (command) {
        case 'batchEnable':
          this.batchUpdateStatus(true)
          break
        case 'batchDisable':
          this.batchUpdateStatus(false)
          break
        case 'batchDelete':
          this.batchDelete()
          break
      }
    },

    // 批量更新状态
    batchUpdateStatus(enabled) {
      const action = enabled ? '启用' : '禁用'
      const selectedIds = this.multipleSelection.map(item => item.id)

      this.$confirm(`确定要${action}选中的 ${selectedIds.length} 个配置吗？`, '批量操作确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.batchLoading = true

        // 批量更新请求
        const promises = selectedIds.map(id => {
          const config = this.multipleSelection.find(item => item.id === id)
          return this.$request({
            url: `/vue-element-admin/filter-configs/${id}`,
            method: 'put',
            data: { ...config, enabled }
          })
        })

        Promise.all(promises).then(() => {
          this.$message.success(`批量${action}成功`)
          this.getList() // 重新获取列表
          this.multipleSelection = [] // 清空选择
        }).catch(error => {
          console.error(`批量${action}失败:`, error)
          this.$message.error(`批量${action}失败，请检查网络连接`)
        }).finally(() => {
          this.batchLoading = false
        })
      })
    },

    // 批量删除
    batchDelete() {
      const selectedIds = this.multipleSelection.map(item => item.id)

      this.$confirm(`确定要删除选中的 ${selectedIds.length} 个配置吗？此操作不可恢复！`, '批量删除确认', {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'error'
      }).then(() => {
        this.batchLoading = true

        // 批量删除请求
        const promises = selectedIds.map(id => {
          return this.$request({
            url: `/vue-element-admin/filter-configs/${id}`,
            method: 'delete'
          })
        })

        Promise.all(promises).then(() => {
          this.$message.success('批量删除成功')
          this.getList() // 重新获取列表
          this.multipleSelection = [] // 清空选择
        }).catch(error => {
          console.error('批量删除失败:', error)
          this.$message.error('批量删除失败，请检查网络连接')
        }).finally(() => {
          this.batchLoading = false
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.config-name {
  font-weight: bold;
  color: #606266;
}

.cost-text {
  color: #606266;
  font-weight: bold;
}

.roi-text {
  color: #606266;
  font-weight: bold;
}

.app-id {
  color: #909399;
  font-weight: bold;
}

.text-muted {
  color: #c0c4cc;
  font-style: italic;
}

.filter-container {
  margin-bottom: 20px;
}

.filter-item {
  margin-right: 10px;
}

.el-tag {
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.tags-preview {
  margin-top: 8px;
  padding: 8px;
  background: #f5f7fa;
  border-radius: 4px;
  min-height: 32px;
}

/* uids相关样式 */
.form-tip {
  margin-top: 5px;
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
}

.uids-preview {
  margin-top: 8px;
  padding: 8px;
  background: #f5f7fa;
  border-radius: 4px;
  min-height: 32px;
}

.uids-display {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 2px;
}

.more-uids {
  font-size: 12px;
  color: #909399;
  margin-left: 5px;
}

/* 操作按钮样式优化 */
.el-table .small-padding .el-button--text {
  font-size: 12px !important;
  padding: 2px 4px !important;
  margin: 0 2px !important;
}

.el-table .small-padding .el-button--text i {
  margin-right: 2px;
  font-size: 12px;
}

.el-table .small-padding .el-button--text span {
  font-size: 12px;
}

.el-table .small-padding {
  font-size: xx-small;
}

/* 配置组名称和状态样式 */
.config-name {
  font-weight: 500;
  color: #606266;
}

.app-id {
  font-size: 13px;
  color: #606266;
  font-weight: 500;
}

.app-id.clickable {
  cursor: pointer;
  transition: all 0.2s ease;
}

.app-id.clickable:hover {
  color: #409EFF;
  text-decoration: underline;
}
</style>
