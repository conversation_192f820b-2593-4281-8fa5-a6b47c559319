<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.nameSearch"
        placeholder="搜索应用名称"
        style="width: 160px;"
        class="filter-item"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.versionSearch"
        placeholder="搜索版本信息"
        style="width: 160px;"
        class="filter-item"
        @keyup.enter.native="handleFilter"
      />
      <div class="filter-item min-cost-container">
        <span class="min-cost-label">最低花费：</span>
        <el-input-number
          v-model="listQuery.minCost"
          placeholder="最低花费"
          style="width: 120px;"
          :precision="2"
          :min="1"
          :max="1000"
          :step="1"
          controls-position="right"
          @keyup.enter.native="handleFilter"
        />
        <span class="min-cost-tip">元</span>
      </div>
      <el-button
        v-waves
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleFilter"
      >
        搜索
      </el-button>
      <el-button
        v-waves
        class="filter-item"
        type="info"
        icon="el-icon-refresh"
        @click="handleReset"
      >
        重置
      </el-button>
    </div>

    <el-table
      :key="tableKey"
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%;"
    >
      <el-table-column label="ID" prop="id" sortable="custom" align="center" width="80">
        <template slot-scope="{row}">
          <span>{{ row.id }}</span>
        </template>
      </el-table-column>
      <el-table-column label="用户ID" width="100px" align="center">
        <template slot-scope="{row}">
          <span>{{ row.uid }}</span>
        </template>
      </el-table-column>
      <el-table-column label="应用名称" min-width="150px">
        <template slot-scope="{row}">
          <span class="link-type" @click="handleUpdate(row)">{{ row.appName || row.appId }}</span>
        </template>
      </el-table-column>
      <el-table-column label="应用ID" width="120px" align="center">
        <template slot-scope="{row}">
          <span>{{ row.appId }}</span>
        </template>
      </el-table-column>
      <el-table-column label="版本" min-width="200px" align="left">
        <template slot-scope="{row}">
          <el-tooltip :content="row.onlineVersion || '未知'" placement="top" :disabled="!row.onlineVersion">
            <span class="version-text">{{ formatVersion(row.onlineVersion) }}</span>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="花费" width="110px" align="center">
        <template slot-scope="{row}">
          <span>¥{{ (row.cost || 0).toFixed(2) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="开始时间" width="120px" align="center">
        <template slot-scope="{row}">
          <span>{{ row.start_date }}</span>
        </template>
      </el-table-column>
      <el-table-column label="结束时间" width="120px" align="center">
        <template slot-scope="{row}">
          <span>{{ row.end_date }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" class-name="status-col" width="100">
        <template slot-scope="{row}">
          <el-tag :type="statusTagType(row.status)">
            {{ statusText(row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="100" class-name="small-padding fixed-width">
        <template slot-scope="{row}">
          <el-button type="primary" size="mini" @click="handleUpdate(row)">
            查看
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.limit"
      @pagination="getList"
    />
  </div>
</template>

<script>
import waves from '@/directive/waves' // waves directive
import Pagination from '@/components/Pagination' // secondary package based on el-pagination

export default {
  name: 'CampaignData',
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      tableKey: 0,
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        limit: 20,
        nameSearch: undefined,
        versionSearch: undefined,
        minCost: 1  // 默认最低花费为1
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.listLoading = true

      // 构建查询参数
      const params = {
        page: this.listQuery.page,
        limit: this.listQuery.limit
      }

      if (this.listQuery.nameSearch) {
        params.name_search = this.listQuery.nameSearch
      }

      if (this.listQuery.versionSearch) {
        params.version_search = this.listQuery.versionSearch
      }

      if (this.listQuery.minCost !== null && this.listQuery.minCost !== undefined) {
        params.min_cost = this.listQuery.minCost
      }

      // 调用投放账户API
      this.$request({
        url: '/vue-element-admin/accounts',
        method: 'get',
        params: params
      }).then(response => {
        if (response.code === 20000) {
          this.list = response.data.items || []
          this.total = response.data.total || 0
        } else {
          this.$message.error(response.message || '获取数据失败')
        }
        this.listLoading = false
      }).catch(error => {
        console.error('获取投放数据失败:', error)
        this.$message.error('获取数据失败')
        this.listLoading = false
      })
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    handleReset() {
      this.listQuery.nameSearch = undefined
      this.listQuery.versionSearch = undefined
      this.listQuery.minCost = 1  // 重置为默认值1
      this.listQuery.page = 1
      this.getList()
    },
    handleUpdate(row) {
      this.$message.success(`查看投放活动: ${row.appName || row.appId}`)
    },
    formatVersion(version) {
      if (!version) return '未知'

      // 如果版本信息太长，截取前50个字符并添加省略号
      if (version.length > 50) {
        return version.substring(0, 50) + '...'
      }
      return version
    },
    statusTagType(status) {
      const typeMap = {
        'active': 'success',
        'paused': 'warning',
        'completed': 'info'
      }
      return typeMap[status] || 'info'
    },
    statusText(status) {
      const textMap = {
        'active': '进行中',
        'paused': '已暂停',
        'completed': '已完成'
      }
      return textMap[status] || status
    }
  }
}
</script>

<style scoped>
.link-type {
  color: #337ab7;
  cursor: pointer;
}
.link-type:hover {
  color: #23527c;
  text-decoration: underline;
}
.version-text {
  font-size: 12px;
  color: #606266;
  line-height: 1.4;
  word-break: break-all;
}
.min-cost-container {
  display: inline-flex;
  align-items: center;
  margin-right: 10px;
}
.min-cost-label {
  font-size: 14px;
  color: #606266;
  margin-right: 8px;
  white-space: nowrap;
}
.min-cost-tip {
  font-size: 14px;
  color: #606266;
  margin-left: 5px;
}
</style>
