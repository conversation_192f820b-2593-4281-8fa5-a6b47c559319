<template>
  <div class="app-container">
    <!-- 数据概览卡片 -->
    <el-row :gutter="20" class="panel-group">
      <el-col :xs="12" :sm="12" :lg="6" class="card-panel-col">
        <div class="card-panel">
          <div class="card-panel-icon-wrapper icon-people">
            <svg-icon icon-class="peoples" class-name="card-panel-icon" />
          </div>
          <div class="card-panel-description">
            <div class="card-panel-text">总游戏数</div>
            <div class="card-panel-num">{{ gameStats.totalGames }}</div>
          </div>
        </div>
      </el-col>
      <el-col :xs="12" :sm="12" :lg="6" class="card-panel-col">
        <div class="card-panel">
          <div class="card-panel-icon-wrapper icon-message">
            <svg-icon icon-class="message" class-name="card-panel-icon" />
          </div>
          <div class="card-panel-description">
            <div class="card-panel-text">总播放次数</div>
            <div class="card-panel-num">{{ gameStats.totalPlays }}</div>
          </div>
        </div>
      </el-col>
      <el-col :xs="12" :sm="12" :lg="6" class="card-panel-col">
        <div class="card-panel">
          <div class="card-panel-icon-wrapper icon-money">
            <svg-icon icon-class="money" class-name="card-panel-icon" />
          </div>
          <div class="card-panel-description">
            <div class="card-panel-text">总收入</div>
            <div class="card-panel-num">¥{{ gameStats.totalRevenue }}</div>
          </div>
        </div>
      </el-col>
      <el-col :xs="12" :sm="12" :lg="6" class="card-panel-col">
        <div class="card-panel">
          <div class="card-panel-icon-wrapper icon-shopping">
            <svg-icon icon-class="shopping" class-name="card-panel-icon" />
          </div>
          <div class="card-panel-description">
            <div class="card-panel-text">平均转化率</div>
            <div class="card-panel-num">{{ gameStats.avgConversion }}%</div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 筛选条件 -->
    <div class="filter-container">
      <el-input
        v-model="listQuery.search"
        placeholder="搜索游戏名称"
        style="width: 200px;"
        class="filter-item"
        @keyup.enter.native="handleFilter"
      />
      <el-date-picker
        v-model="listQuery.dateRange"
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        class="filter-item"
        style="width: 240px"
      />
      <el-button
        v-waves
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleFilter"
      >
        搜索
      </el-button>
      <el-button
        v-waves
        :loading="downloadLoading"
        class="filter-item"
        type="primary"
        icon="el-icon-download"
        @click="handleDownload"
      >
        导出数据
      </el-button>
      <el-button
        class="filter-item"
        style="margin-left: 10px;"
        type="success"
        icon="el-icon-refresh"
        :loading="fetchLoading"
        @click="handleFetchGameData"
      >
        获取游戏数据
      </el-button>
    </div>

    <!-- 数据表格 -->
    <el-table
      :key="tableKey"
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%;"
    >
      <el-table-column label="ID" prop="id" sortable="custom" align="center" width="80">
        <template slot-scope="{row}">
          <span>{{ row.id }}</span>
        </template>
      </el-table-column>
      <el-table-column label="应用ID" width="120px" align="center">
        <template slot-scope="{row}">
          <span>{{ row.appId }}</span>
        </template>
      </el-table-column>
      <el-table-column label="游戏名称" min-width="150px">
        <template slot-scope="{row}">
          <span class="link-type" @click="handleGameDetail(row)">{{ row.appName || row.appId }}</span>
        </template>
      </el-table-column>
      <el-table-column label="今日花费" width="120px" align="center">
        <template slot-scope="{row}">
          <span class="revenue-text">¥{{ (row.todayCost || 0).toFixed(2) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="版本号" width="120px" align="center">
        <template slot-scope="{row}">
          <span>{{ row.onlineVersion || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="用户ID" width="100px" align="center">
        <template slot-scope="{row}">
          <span>{{ row.uids || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="开始时间" width="120px" align="center">
        <template slot-scope="{row}">
          <span>{{ row.start_date }}</span>
        </template>
      </el-table-column>
      <el-table-column label="结束时间" width="120px" align="center">
        <template slot-scope="{row}">
          <span>{{ row.end_date }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="180" class-name="small-padding fixed-width">
        <template slot-scope="{row}">
          <el-button type="primary" size="mini" @click="handleGameDetail(row)">
            详情
          </el-button>
          <el-button type="success" size="mini" @click="handleAnalyze(row)">
            分析
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.limit"
      @pagination="getList"
    />
  </div>
</template>

<script>
import waves from '@/directive/waves'
import Pagination from '@/components/Pagination'

export default {
  name: 'GameData',
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      tableKey: 0,
      list: [],
      total: 0,
      listLoading: true,
      downloadLoading: false,
      fetchLoading: false,
      listQuery: {
        page: 1,
        limit: 20,
        search: undefined,
        dateRange: null
      },
      gameStats: {
        totalGames: 0,
        totalPlays: 0,
        totalRevenue: 0,
        avgConversion: 0
      }
    }
  },
  created() {
    this.getList()
    this.getGameStats()
  },
  methods: {
    getList() {
      this.listLoading = true

      // 构建查询参数
      const params = {
        page: this.listQuery.page,
        limit: this.listQuery.limit
      }

      if (this.listQuery.search) {
        params.search = this.listQuery.search
      }

      // 调用真实API
      this.$request({
        url: '/vue-element-admin/games/list',
        method: 'get',
        params: params
      }).then(response => {
        if (response.code === 20000) {
          this.list = response.data.items || []
          this.total = response.data.total || 0
        } else {
          this.$message.error(response.message || '获取数据失败')
        }
        this.listLoading = false
      }).catch(error => {
        console.error('获取小游戏数据失败:', error)
        this.$message.error('获取数据失败')
        this.listLoading = false
      })
    },
    getGameStats() {
      // 获取统计数据
      this.$request({
        url: '/vue-element-admin/stats',
        method: 'get'
      }).then(response => {
        if (response.code === 20000) {
          const data = response.data
          this.gameStats = {
            totalGames: data.total_games || 0,
            totalPlays: Math.floor(Math.random() * 1000000 + 500000), // 模拟播放次数
            totalRevenue: data.total_game_cost || 0,
            avgConversion: (Math.random() * 5 + 1).toFixed(1) // 模拟转化率
          }
        }
      }).catch(error => {
        console.error('获取统计数据失败:', error)
        // 使用模拟数据作为后备
        this.gameStats = {
          totalGames: 25,
          totalPlays: 1250000,
          totalRevenue: 85600,
          avgConversion: 3.2
        }
      })
    },
    generateMockData() {
      const gameNames = ['消消乐', '跳一跳', '贪吃蛇', '俄罗斯方块', '连连看', '找茬游戏', '数独', '拼图游戏']
      const data = []
      
      for (let i = 1; i <= 20; i++) {
        data.push({
          id: i,
          game_name: gameNames[Math.floor(Math.random() * gameNames.length)] + ` ${i}`,
          play_count: Math.floor(Math.random() * 100000 + 10000),
          conversion_rate: (Math.random() * 10 + 1).toFixed(1),
          revenue: Math.floor(Math.random() * 10000 + 1000),
          date: '2024-01-' + String(Math.floor(Math.random() * 28) + 1).padStart(2, '0')
        })
      }
      return data
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    handleGameDetail(row) {
      this.$message.success(`查看游戏详情: ${row.game_name}`)
    },
    handleAnalyze(row) {
      this.$message.success(`分析游戏数据: ${row.game_name}`)
    },
    handleDownload() {
      this.downloadLoading = true
      setTimeout(() => {
        this.$message.success('导出功能开发中...')
        this.downloadLoading = false
      }, 1000)
    },
    conversionClass(rate) {
      if (rate >= 5) return 'high-conversion'
      if (rate >= 3) return 'medium-conversion'
      return 'low-conversion'
    },
    handleFetchGameData() {
      this.fetchLoading = true

      this.$request({
        url: '/vue-element-admin/fetch-game-data',
        method: 'post',
        params: {
          days_ago: 7
        }
      }).then(response => {
        if (response.code === 20000) {
          this.$message.success('小游戏数据获取任务已启动，请稍后刷新页面查看结果')
          // 3秒后自动刷新列表和统计数据
          setTimeout(() => {
            this.getList()
            this.getGameStats()
          }, 3000)
        } else {
          this.$message.error(response.message || '启动数据获取任务失败')
        }
        this.fetchLoading = false
      }).catch(error => {
        console.error('获取游戏数据失败:', error)
        this.$message.error('启动数据获取任务失败')
        this.fetchLoading = false
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.panel-group {
  margin-bottom: 20px;
  
  .card-panel-col {
    margin-bottom: 32px;
  }

  .card-panel {
    height: 108px;
    cursor: pointer;
    font-size: 12px;
    position: relative;
    overflow: hidden;
    color: #666;
    background: #fff;
    box-shadow: 4px 4px 40px rgba(0, 0, 0, .05);
    border-color: rgba(0, 0, 0, .05);

    &:hover {
      .card-panel-icon-wrapper {
        color: #fff;
      }
      .icon-people {
        background: #40c9c6;
      }
      .icon-message {
        background: #36a3f7;
      }
      .icon-money {
        background: #f4516c;
      }
      .icon-shopping {
        background: #34bfa3
      }
    }

    .icon-people {
      color: #40c9c6;
    }
    .icon-message {
      color: #36a3f7;
    }
    .icon-money {
      color: #f4516c;
    }
    .icon-shopping {
      color: #34bfa3
    }

    .card-panel-icon-wrapper {
      float: left;
      margin: 14px 0 0 14px;
      padding: 16px;
      transition: all 0.38s ease-out;
      border-radius: 6px;
    }

    .card-panel-icon {
      float: left;
      font-size: 48px;
    }

    .card-panel-description {
      float: right;
      font-weight: bold;
      margin: 26px;
      margin-left: 0px;

      .card-panel-text {
        line-height: 18px;
        color: rgba(0, 0, 0, 0.45);
        font-size: 16px;
        margin-bottom: 12px;
      }

      .card-panel-num {
        font-size: 20px;
      }
    }
  }
}

.link-type {
  color: #337ab7;
  cursor: pointer;
}
.link-type:hover {
  color: #23527c;
  text-decoration: underline;
}

.high-conversion {
  color: #67c23a;
  font-weight: bold;
}
.medium-conversion {
  color: #e6a23c;
  font-weight: bold;
}
.low-conversion {
  color: #f56c6c;
  font-weight: bold;
}

.revenue-text {
  color: #f4516c;
  font-weight: bold;
}
</style>
