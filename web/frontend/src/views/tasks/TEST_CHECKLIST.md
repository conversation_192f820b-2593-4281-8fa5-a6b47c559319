# 任务管理页面功能测试清单

## 重构后功能验证

### ✅ 已修复的问题

1. **类型错误修复**
   - ✅ 修复了 `el-tabs` 的 `currentName` 类型错误
   - ✅ 将 `currentViewId` 保持为字符串类型
   - ✅ 在 API 调用时正确转换为数字类型

2. **组件通信修复**
   - ✅ 移除了 `v-model` 和 `.sync` 修饰符，避免直接修改 props
   - ✅ 使用事件通信替代直接 prop 修改

### 🧪 功能测试清单

#### 1. 视图管理功能
- [ ] **默认视图加载**: 页面加载时自动选中"全部任务"视图
- [ ] **视图切换**: 点击不同视图标签页，任务列表正确更新
- [ ] **视图创建**: 点击"新建视图"按钮，弹出创建对话框
- [ ] **视图编辑**: 点击"编辑视图"按钮，弹出编辑对话框
- [ ] **视图删除**: 点击"删除视图"按钮，确认删除后视图消失

#### 2. 任务列表功能
- [ ] **任务显示**: 任务列表正确显示所有字段
- [ ] **状态显示**: 任务状态用不同颜色的标签显示
- [ ] **动态按钮**: 运行中的任务显示"停止"按钮，其他显示"立即执行"
- [ ] **搜索功能**: 输入关键词搜索任务
- [ ] **分页功能**: 分页组件正常工作

#### 3. 任务操作功能
- [ ] **立即执行**: 点击"立即执行"按钮，任务开始运行
- [ ] **停止任务**: 点击"停止"按钮，运行中的任务被停止
- [ ] **编辑任务**: 点击"编辑"按钮，弹出编辑对话框
- [ ] **复制任务**: 点击"复制"按钮，弹出创建对话框（预填数据）
- [ ] **删除任务**: 点击"删除"按钮，确认删除后任务消失
- [ ] **启用/禁用**: 切换开关，任务状态正确更新

#### 4. 任务表单功能
- [ ] **创建任务**: 填写表单创建新任务
- [ ] **编辑任务**: 修改现有任务信息
- [ ] **表单验证**: 必填字段验证正常
- [ ] **脚本路径验证**: Python 文件路径格式验证
- [ ] **Cron 表达式**: 可以输入有效的 Cron 表达式

#### 5. 日志查看功能
- [ ] **实时日志**: 点击"查看日志"显示实时日志
- [ ] **历史日志**: 切换到历史模式显示日志列表
- [ ] **日志详情**: 点击历史日志条目查看详情
- [ ] **实时更新**: 运行中任务的日志实时更新
- [ ] **停止按钮**: 在日志弹窗中可以停止任务

#### 6. 状态管理功能
- [ ] **状态同步**: 任务状态在列表和日志中保持同步
- [ ] **自动刷新**: 执行任务后列表自动刷新
- [ ] **错误处理**: API 错误时显示正确的错误信息

### 🐛 已知问题检查

#### 1. 控制台错误
- [ ] 无 Vue 警告信息
- [ ] 无 JavaScript 错误
- [ ] 无网络请求错误

#### 2. 性能问题
- [ ] 页面加载速度正常
- [ ] 视图切换响应及时
- [ ] 实时日志更新不卡顿

#### 3. 用户体验
- [ ] 按钮状态正确反映任务状态
- [ ] 加载状态显示正常
- [ ] 成功/错误消息提示正常

### 📋 测试步骤

#### 基础功能测试
1. **打开任务管理页面**
   ```
   访问: http://localhost:9527/#/tasks
   ```

2. **测试视图切换**
   - 点击"全部任务"标签
   - 点击其他视图标签
   - 观察任务列表是否正确更新

3. **测试任务执行**
   - 找一个状态为"等待中"的任务
   - 点击"立即执行"按钮
   - 观察按钮是否变为"停止"
   - 点击"查看日志"查看实时输出

4. **测试停止功能**
   - 在运行中的任务上点击"停止"
   - 确认任务被正确停止
   - 观察状态是否更新为"失败"

#### 高级功能测试
1. **测试视图管理**
   - 创建新视图
   - 编辑现有视图
   - 删除视图

2. **测试任务管理**
   - 创建新任务
   - 编辑现有任务
   - 复制任务
   - 删除任务

3. **测试日志功能**
   - 查看实时日志
   - 切换历史模式
   - 查看日志详情

### 🔧 故障排除

#### 如果视图切换不工作
1. 检查浏览器控制台是否有错误
2. 检查网络请求是否成功
3. 检查 `currentViewId` 的类型是否正确

#### 如果任务执行失败
1. 检查脚本路径是否正确
2. 检查后端服务是否正常运行
3. 检查任务调度器是否启动

#### 如果日志不显示
1. 检查任务是否真的在运行
2. 检查实时日志 API 是否正常
3. 检查日志组件是否正确加载

### ✅ 测试完成标准

所有功能测试项目都通过，且：
- 无控制台错误
- 用户操作流畅
- 数据显示正确
- 状态同步及时

### 📝 测试报告模板

```
测试日期: ____
测试人员: ____
浏览器版本: ____

功能测试结果:
- 视图管理: ✅/❌
- 任务列表: ✅/❌  
- 任务操作: ✅/❌
- 任务表单: ✅/❌
- 日志查看: ✅/❌
- 状态管理: ✅/❌

发现问题:
1. ____
2. ____

总体评价: ✅ 通过 / ❌ 需要修复
```
