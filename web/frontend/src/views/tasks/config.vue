<template>
  <div class="app-container">
    <div class="config-header">
      <h2>任务配置管理</h2>
      <p>统一管理所有任务的默认配置，参考青龙面板设计</p>
    </div>

    <el-row :gutter="20">
      <!-- 基础配置 -->
      <el-col :span="12">
        <el-card class="config-card">
          <div slot="header" class="clearfix">
            <span>基础配置</span>
            <el-button style="float: right; padding: 3px 0" type="text" @click="saveBasicConfig">保存</el-button>
          </div>
          
          <el-form :model="basicConfig" label-width="120px">
            <el-form-item label="最大实例数">
              <el-input-number
                v-model="basicConfig.max_instances"
                :min="1"
                :max="10"
                style="width: 200px;"
              />
              <div class="config-tip">同时运行的最大任务实例数</div>
            </el-form-item>
            
            <el-form-item label="默认超时(秒)">
              <el-input-number
                v-model="basicConfig.default_timeout"
                :min="0"
                :max="3600"
                style="width: 200px;"
              />
              <div class="config-tip">任务执行超时时间，0表示不限制</div>
            </el-form-item>
            
            <el-form-item label="日志保留天数">
              <el-input-number
                v-model="basicConfig.log_retention_days"
                :min="1"
                :max="365"
                style="width: 200px;"
              />
              <div class="config-tip">自动清理多少天前的执行日志</div>
            </el-form-item>
            
            <el-form-item label="失败重试次数">
              <el-input-number
                v-model="basicConfig.retry_count"
                :min="0"
                :max="5"
                style="width: 200px;"
              />
              <div class="config-tip">任务执行失败时的重试次数</div>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>

      <!-- 环境变量配置 -->
      <el-col :span="12">
        <el-card class="config-card">
          <div slot="header" class="clearfix">
            <span>全局环境变量</span>
            <el-button style="float: right; padding: 3px 0" type="text" @click="saveEnvConfig">保存</el-button>
          </div>
          
          <el-form label-width="120px">
            <el-form-item label="环境变量">
              <el-input
                v-model="envVarsText"
                type="textarea"
                :rows="8"
                placeholder="请输入全局环境变量，格式：KEY1=value1&#10;KEY2=value2"
                style="width: 100%;"
              />
              <div class="config-tip">这些环境变量将应用到所有任务</div>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 20px;">
      <!-- 通知配置 -->
      <el-col :span="12">
        <el-card class="config-card">
          <div slot="header" class="clearfix">
            <span>通知配置</span>
            <el-button style="float: right; padding: 3px 0" type="text" @click="saveNotificationConfig">保存</el-button>
          </div>
          
          <el-form :model="notificationConfig" label-width="120px">
            <el-form-item label="启用通知">
              <el-switch v-model="notificationConfig.enabled" />
              <div class="config-tip">任务执行失败时发送通知</div>
            </el-form-item>
            
            <el-form-item label="通知方式" v-if="notificationConfig.enabled">
              <el-checkbox-group v-model="notificationConfig.methods">
                <el-checkbox label="email">邮件</el-checkbox>
                <el-checkbox label="webhook">Webhook</el-checkbox>
                <el-checkbox label="dingtalk">钉钉</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            
            <el-form-item label="Webhook URL" v-if="notificationConfig.methods.includes('webhook')">
              <el-input
                v-model="notificationConfig.webhook_url"
                placeholder="请输入Webhook地址"
                style="width: 100%;"
              />
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>

      <!-- 系统配置 -->
      <el-col :span="12">
        <el-card class="config-card">
          <div slot="header" class="clearfix">
            <span>系统配置</span>
            <el-button style="float: right; padding: 3px 0" type="text" @click="saveSystemConfig">保存</el-button>
          </div>
          
          <el-form :model="systemConfig" label-width="120px">
            <el-form-item label="Python路径">
              <el-input
                v-model="systemConfig.python_path"
                placeholder="默认使用系统Python"
                style="width: 100%;"
              />
              <div class="config-tip">指定执行任务的Python解释器路径</div>
            </el-form-item>
            
            <el-form-item label="工作目录">
              <el-input
                v-model="systemConfig.work_directory"
                placeholder="默认为backend目录"
                style="width: 100%;"
              />
              <div class="config-tip">任务执行时的工作目录</div>
            </el-form-item>
            
            <el-form-item label="调度器状态">
              <el-switch
                v-model="systemConfig.scheduler_enabled"
                active-text="启用"
                inactive-text="禁用"
              />
              <div class="config-tip">是否启用任务调度器</div>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
    </el-row>

    <!-- 操作按钮 -->
    <div class="config-actions">
      <el-button type="primary" @click="saveAllConfig" :loading="saving">
        保存所有配置
      </el-button>
      <el-button @click="resetConfig">
        重置配置
      </el-button>
      <el-button type="info" @click="exportConfig">
        导出配置
      </el-button>
      <el-button type="warning" @click="importConfig">
        导入配置
      </el-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TaskConfig',
  data() {
    return {
      saving: false,
      basicConfig: {
        max_instances: 1,
        default_timeout: 300,
        log_retention_days: 30,
        retry_count: 0
      },
      envVarsText: '',
      notificationConfig: {
        enabled: false,
        methods: [],
        webhook_url: ''
      },
      systemConfig: {
        python_path: '',
        work_directory: '',
        scheduler_enabled: true
      }
    }
  },
  created() {
    this.loadConfig()
  },
  methods: {
    loadConfig() {
      // 加载配置数据
      // TODO: 从API加载配置
      console.log('加载配置数据')
    },
    saveBasicConfig() {
      this.$message.success('基础配置保存成功')
    },
    saveEnvConfig() {
      this.$message.success('环境变量配置保存成功')
    },
    saveNotificationConfig() {
      this.$message.success('通知配置保存成功')
    },
    saveSystemConfig() {
      this.$message.success('系统配置保存成功')
    },
    saveAllConfig() {
      this.saving = true
      setTimeout(() => {
        this.saving = false
        this.$message.success('所有配置保存成功')
      }, 1000)
    },
    resetConfig() {
      this.$confirm('确定要重置所有配置吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$message.success('配置重置成功')
      })
    },
    exportConfig() {
      this.$message.info('配置导出功能开发中')
    },
    importConfig() {
      this.$message.info('配置导入功能开发中')
    }
  }
}
</script>

<style scoped>
.config-header {
  margin-bottom: 20px;
  padding: 20px;
  background: #f5f7fa;
  border-radius: 4px;
}

.config-header h2 {
  margin: 0 0 10px 0;
  color: #303133;
}

.config-header p {
  margin: 0;
  color: #606266;
}

.config-card {
  margin-bottom: 20px;
}

.config-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

.config-actions {
  text-align: center;
  padding: 20px;
  border-top: 1px solid #ebeef5;
  margin-top: 20px;
}

.config-actions .el-button {
  margin: 0 10px;
}
</style>
