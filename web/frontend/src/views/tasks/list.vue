<template>
  <div class="app-container">
    <!-- 视图管理组件 -->
    <TaskViewManager
      :task-views="taskViews"
      :current-view-id="currentViewId"
      @view-change="handleViewChange"
      @create-view="handleCreateView"
      @update-view="handleUpdateView"
      @delete-view="handleDeleteView"
    />

    <!-- 任务列表 -->
    <div class="filter-container">
      <el-input v-model="listQuery.search" placeholder="搜索任务" style="width: 200px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">
        搜索
      </el-button>
      <el-button class="filter-item" style="margin-left: 10px;" type="primary" icon="el-icon-edit" @click="handleCreate">
        添加任务
      </el-button>
    </div>

    <el-table
      :key="tableKey"
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%;"
    >
      <el-table-column label="ID" prop="id" align="center" width="80">
        <template slot-scope="{row}">
          <span>{{ row.id }}</span>
        </template>
      </el-table-column>

      <el-table-column label="任务名称" min-width="150px">
        <template slot-scope="{row}">
          <el-link 
            type="primary" 
            @click="handleViewHistoryLogs(row)"
            :title="'点击查看历史日志'"
          >
            {{ row.name }}
          </el-link>
        </template>
      </el-table-column>

      <el-table-column label="脚本路径" min-width="200px" align="center">
        <template slot-scope="{row}">
          <span>{{ row.script_path }}</span>
        </template>
      </el-table-column>

      <el-table-column label="Cron表达式" width="150px" align="center">
        <template slot-scope="{row}">
          <span>{{ row.cron_expression || '-' }}</span>
        </template>
      </el-table-column>

      <el-table-column label="状态" width="100px" align="center">
        <template slot-scope="{row}">
          <el-tag :type="getStatusType(row.status)" size="mini">
            {{ getStatusText(row.status) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="启用状态" width="100px" align="center">
        <template slot-scope="{row}">
          <el-switch
            v-model="row.enabled"
            @change="handleToggleEnabled(row)"
          />
        </template>
      </el-table-column>

      <el-table-column label="最后运行时间" width="160px" align="center">
        <template slot-scope="{row}">
          <span>{{ row.last_run_time || '-' }}</span>
        </template>
      </el-table-column>

      <el-table-column label="最后运行时长" width="120px" align="center">
        <template slot-scope="{row}">
          <span>{{ formatDuration(row.last_run_duration) }}</span>
        </template>
      </el-table-column>

      <el-table-column label="下次运行" width="160px" align="center">
        <template slot-scope="{row}">
          <span>{{ row.next_run_time || '-' }}</span>
        </template>
      </el-table-column>

      <el-table-column label="操作" align="center" width="360px" class-name="small-padding fixed-width">
        <template slot-scope="{row}">
          <el-button 
            :type="row.status === 'running' ? 'danger' : 'primary'" 
            size="mini" 
            @click="row.status === 'running' ? handleStopTask(row) : handleRunNow(row)"
          >
            {{ row.status === 'running' ? '停止' : '立即执行' }}
          </el-button>
          <el-button type="info" size="mini" @click="handleViewLogs(row)">
            查看日志
          </el-button>
          <el-button type="warning" size="mini" @click="handleEdit(row)">
            编辑
          </el-button>
          <el-button type="success" size="mini" @click="handleCopy(row)">
            复制
          </el-button>
          <el-button
            size="mini"
            type="danger"
            @click="handleDelete(row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />

    <!-- 任务表单对话框 -->
    <TaskFormDialog
      :visible.sync="dialogFormVisible"
      :dialog-status="dialogStatus"
      :temp="temp"
      @submit="handleFormSubmit"
      @close="resetTemp"
    />

    <!-- 任务日志对话框 -->
    <TaskLogDialog
      :visible.sync="logDialogVisible"
      :current-task="currentTask"
      @stop-task="handleStopTaskFromDialog"
      @fetch-latest-log="fetchLatestLog"
      @fetch-latest-log-for-realtime="fetchLatestLogForRealTimeMode"
      @fetch-task-logs="fetchTaskLogs"
      @view-log-detail="handleViewLogDetail"
      ref="logDialog"
    />

    <!-- 日志详情对话框 -->
    <el-dialog 
      title="执行日志详情" 
      :visible.sync="logDetailVisible" 
      width="55%" 
      top="5vh"
      class="log-detail-dialog"
    >
      <div v-if="currentLogDetail">
        <div class="log-detail-info">
          <div class="info-row">
            <div class="info-item">
              <span class="info-label">日志ID:</span>
              <span class="info-value">{{ currentLogDetail.id }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">任务ID:</span>
              <span class="info-value">{{ currentLogDetail.task_id }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">状态:</span>
              <el-tag :type="getStatusType(currentLogDetail.status)" size="mini">
                {{ getStatusText(currentLogDetail.status) }}
              </el-tag>
            </div>
          </div>
          <div class="info-row">
            <div class="info-item">
              <span class="info-label">开始时间:</span>
              <span class="info-value">{{ currentLogDetail.start_time || '-' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">结束时间:</span>
              <span class="info-value">{{ currentLogDetail.end_time || '-' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">运行时长:</span>
              <span class="info-value">{{ formatDuration(currentLogDetail.duration) }}</span>
            </div>
          </div>
          <div class="info-row">
            <div class="info-item">
              <span class="info-label">退出码:</span>
              <span class="info-value">{{ currentLogDetail.exit_code !== null ? currentLogDetail.exit_code : '-' }}</span>
            </div>
          </div>
        </div>

        <div style="margin-top: 10px;">
          <h4 style="margin-bottom: 8px;">完整日志输出:</h4>
          <el-input
            :value="getCompleteLog(currentLogDetail)"
            type="textarea"
            :rows="calculateTextareaRows(getCompleteLog(currentLogDetail))"
            readonly
            placeholder="无日志输出"
            class="log-textarea"
            style="font-family: 'Courier New', monospace; font-size: 14px; width: 100%; max-width: 100%;"
          />
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getTaskList, deleteTask, runTaskNow, updateTask, createTask, getTaskLogs, getLatestTaskLog, getTaskViews, createTaskView, updateTaskView, deleteTaskView, getTasksByView } from '@/api/tasks'
import waves from '@/directive/waves'
import Pagination from '@/components/Pagination'
import TaskViewManager from './components/TaskViewManager'
import TaskFormDialog from './components/TaskFormDialog'
import TaskLogDialog from './components/TaskLogDialog'

export default {
  name: 'TaskList',
  components: { 
    Pagination, 
    TaskViewManager, 
    TaskFormDialog, 
    TaskLogDialog 
  },
  directives: { waves },
  data() {
    return {
      tableKey: 0,
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        limit: 20,
        search: undefined
      },
      // 视图管理
      taskViews: [],
      currentViewId: null,
      // 表单对话框
      dialogFormVisible: false,
      dialogStatus: '',
      temp: {
        id: undefined,
        name: '',
        script_path: '',
        cron_expression: '',
        description: '',
        enabled: true
      },
      // 日志相关
      logDialogVisible: false,
      logDetailVisible: false,
      currentTask: null,
      currentLogDetail: null
    }
  },
  created() {
    this.getTaskViews()
  },
  methods: {
    // 基础方法
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },

    resetTemp() {
      this.temp = {
        id: undefined,
        name: '',
        script_path: '',
        cron_expression: '',
        description: '',
        enabled: true
      }
    },

    // 获取数据
    async getTaskViews() {
      try {
        const response = await getTaskViews()
        if (response.code === 20000) {
          this.taskViews = response.data
          if (this.taskViews.length > 0 && !this.currentViewId) {
            this.currentViewId = this.taskViews[0].id.toString() // 转换为字符串
            this.getList()
          }
        }
      } catch (error) {
        console.error('获取任务视图失败:', error)
        this.$message.error('获取任务视图失败')
      }
    },

    async getList() {
      this.listLoading = true
      try {
        let response
        if (this.currentViewId) {
          response = await getTasksByView(parseInt(this.currentViewId), this.listQuery)
        } else {
          response = await getTaskList(this.listQuery)
        }
        
        if (response.code === 20000) {
          this.list = response.data.items
          this.total = response.data.total
        }
      } catch (error) {
        console.error('获取任务列表失败:', error)
        this.$message.error('获取任务列表失败')
      }
      this.listLoading = false
    },

    // 视图管理
    handleViewChange(viewId) {
      this.currentViewId = viewId // 保持字符串类型
      this.getList()
    },

    async handleCreateView(viewData) {
      try {
        const response = await createTaskView(viewData)
        if (response.code === 20000) {
          this.$message.success('创建视图成功')
          this.getTaskViews()
        } else {
          this.$message.error(response.message || '创建视图失败')
        }
      } catch (error) {
        console.error('创建视图失败:', error)
        this.$message.error('创建视图失败')
      }
    },

    async handleUpdateView(viewData) {
      try {
        const response = await updateTaskView(viewData.id, viewData)
        if (response.code === 20000) {
          this.$message.success('更新视图成功')
          this.getTaskViews()
        } else {
          this.$message.error(response.message || '更新视图失败')
        }
      } catch (error) {
        console.error('更新视图失败:', error)
        this.$message.error('更新视图失败')
      }
    },

    async handleDeleteView(viewId) {
      try {
        const response = await deleteTaskView(viewId)
        if (response.code === 20000) {
          this.$message.success('删除视图成功')
          if (this.currentViewId === viewId.toString()) {
            this.currentViewId = this.taskViews[0]?.id?.toString()
          }
          this.getTaskViews()
        } else {
          this.$message.error(response.message || '删除视图失败')
        }
      } catch (error) {
        console.error('删除视图失败:', error)
        this.$message.error('删除视图失败')
      }
    },

    // 任务管理
    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
    },

    handleEdit(row) {
      this.temp = Object.assign({}, row)
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
    },

    async handleFormSubmit(formData) {
      try {
        let response
        if (this.dialogStatus === 'create') {
          response = await createTask(formData)
        } else {
          response = await updateTask(formData.id, formData)
        }

        if (response.code === 20000) {
          this.$message.success(this.dialogStatus === 'create' ? '创建成功' : '更新成功')
          this.getList()
        } else {
          this.$message.error(response.message || '操作失败')
        }
      } catch (error) {
        console.error('操作失败:', error)
        this.$message.error('操作失败')
      }
    },

    handleCopy(row) {
      this.temp = Object.assign({}, row, {
        id: undefined,
        name: row.name + ' (副本)'
      })
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
    },

    handleDelete(row) {
      this.$confirm('确认删除该任务？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const response = await deleteTask(row.id)
          if (response.code === 20000) {
            this.$message.success('删除成功')
            this.getList()
          } else {
            this.$message.error(response.message || '删除失败')
          }
        } catch (error) {
          console.error('删除失败:', error)
          this.$message.error('删除失败')
        }
      })
    },

    async handleToggleEnabled(row) {
      try {
        const response = await updateTask(row.id, { ...row, enabled: row.enabled })
        if (response.code === 20000) {
          this.$message.success(row.enabled ? '启用成功' : '禁用成功')
        } else {
          this.$message.error(response.message || '操作失败')
          row.enabled = !row.enabled // 回滚状态
        }
      } catch (error) {
        console.error('操作失败:', error)
        this.$message.error('操作失败')
        row.enabled = !row.enabled // 回滚状态
      }
    },

    // 任务执行
    async handleRunNow(row) {
      try {
        const response = await runTaskNow(row.id)
        if (response.code === 20000) {
          this.$message.success('任务已开始执行')
          this.getList()
        } else {
          this.$message.error(response.message || '执行失败')
        }
      } catch (error) {
        console.error('执行失败:', error)
        this.$message.error('执行失败')
      }
    },

    async handleStopTask(row) {
      this.$confirm(`确认停止任务 "${row.name}"？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const response = await this.$request({
            url: `/vue-element-admin/tasks/${row.id}/stop`,
            method: 'post'
          })
          if (response.code === 20000) {
            this.$message.success('任务已停止')
            this.getList()
          } else {
            this.$message.error(response.message || '停止任务失败')
          }
        } catch (error) {
          console.error('停止任务失败:', error)
          this.$message.error('停止任务失败')
        }
      })
    },

    handleStopTaskFromDialog(taskId) {
      this.handleStopTask({ id: taskId, name: this.currentTask.name })
    },

    // 日志管理
    handleViewLogs(row) {
      this.currentTask = row
      this.logDialogVisible = true
    },

    handleViewHistoryLogs(row) {
      this.currentTask = row
      this.logDialogVisible = true
      // 通知子组件切换到历史模式
      this.$nextTick(() => {
        if (this.$refs.logDialog) {
          this.$refs.logDialog.isRealTimeMode = false
          this.$refs.logDialog.fetchTaskLogs(row.id)
        }
      })
    },

    async fetchLatestLog(taskId) {
      try {
        const response = await getLatestTaskLog(taskId)
        if (response.code === 20000 && response.data) {
          this.$refs.logDialog.updateLogDetail(response.data)
        }
      } catch (error) {
        console.error('获取最新日志失败:', error)
      }
    },

    async fetchLatestLogForRealTimeMode(taskId) {
      try {
        const response = await getLatestTaskLog(taskId)
        if (response.code === 20000 && response.data) {
          this.$refs.logDialog.updateLogDetail(response.data)
        } else {
          // 创建空日志对象
          this.$refs.logDialog.updateLogDetail({
            id: null,
            task_id: taskId,
            status: 'pending',
            start_time: null,
            end_time: null,
            duration: null,
            exit_code: null,
            output: '',
            error: ''
          })
        }
      } catch (error) {
        console.error('获取日志失败:', error)
      }
    },

    async fetchTaskLogs(taskId) {
      try {
        const response = await getTaskLogs(taskId, { page: 1, limit: 50 })
        if (response.code === 20000) {
          this.$refs.logDialog.updateTaskLogs(response.data.items)
        }
      } catch (error) {
        console.error('获取任务日志失败:', error)
      }
    },

    handleViewLogDetail(log) {
      this.currentLogDetail = log
      this.logDetailVisible = true
    },

    // 工具方法
    getStatusType(status) {
      const statusMap = {
        'pending': 'info',
        'running': 'warning',
        'completed': 'success',
        'failed': 'danger'
      }
      return statusMap[status] || 'info'
    },

    getStatusText(status) {
      const statusMap = {
        'pending': '等待中',
        'running': '运行中',
        'completed': '已完成',
        'failed': '失败'
      }
      return statusMap[status] || status
    },

    getCompleteLog(logDetail) {
      if (!logDetail) return '无日志数据'

      let completeLog = ''
      if (logDetail.output) {
        completeLog += logDetail.output
      }
      if (logDetail.error) {
        if (completeLog) completeLog += '\n\n=== 错误输出 ===\n'
        completeLog += logDetail.error
      }

      if (!completeLog && logDetail.status === 'running') {
        return '任务正在运行中，等待日志输出...\n\n提示：日志可能需要几秒钟才会显示'
      }

      return completeLog || '无日志输出'
    },

    calculateTextareaRows(content) {
      if (!content) return 8

      const lines = content.split('\n').length
      const minRows = 8
      const maxRows = 40

      return Math.max(minRows, Math.min(maxRows, lines + 2))
    },

    formatDuration(duration) {
      if (!duration && duration !== 0) return '-'

      // 确保duration是整数
      const intDuration = Math.round(duration)

      if (intDuration < 60) {
        return `${intDuration}秒`
      } else if (intDuration < 3600) {
        const minutes = Math.floor(intDuration / 60)
        const seconds = intDuration % 60
        return `${minutes}分${seconds}秒`
      } else {
        const hours = Math.floor(intDuration / 3600)
        const minutes = Math.floor((intDuration % 3600) / 60)
        const seconds = intDuration % 60
        return `${hours}时${minutes}分${seconds}秒`
      }
    }
  }
}
</script>

<style>
/* 这里只保留必要的样式，其他样式移到组件中 */
.app-container {
  padding: 20px;
}

.filter-container {
  padding-bottom: 10px;
}

.filter-container .filter-item {
  display: inline-block;
  vertical-align: middle;
  margin-bottom: 10px;
}

/* 日志详情样式 */
.log-detail-info {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

.info-row {
  display: flex;
  gap: 20px;
  margin-bottom: 12px;
  flex-wrap: wrap;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 200px;
  flex: 1;
}

.info-label {
  font-weight: 500;
  color: #606266;
  white-space: nowrap;
  min-width: 70px;
}

.info-value {
  color: #303133;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  word-break: break-all;
}

/* 日志文本区域样式 */
.log-textarea .el-textarea__inner {
  font-family: 'Courier New', 'Monaco', 'Menlo', monospace !important;
  font-size: 14px !important;
  line-height: 1.5 !important;
}
</style>
