<template>
  <!-- 任务日志对话框 -->
  <el-dialog
    title="任务执行日志"
    :visible="visible"
    width="45%"
    top="3vh"
    @close="handleClose"
    class="log-dialog"
  >
    <div style="margin-bottom: 15px;">
      <div style="float: right;">
        <el-switch
          v-model="isRealTimeMode"
          active-text="实时模式"
          inactive-text="历史模式"
          @change="toggleRealTimeMode"
          style="margin-right: 10px;"
        />
        <span v-if="isRealTimeMode && currentLogDetail && currentLogDetail.status === 'running'"
              style="margin-right: 10px; color: #67C23A; font-size: 12px;">
          ● 实时更新中
        </span>
        <span v-if="isRealTimeMode && currentLogDetail && currentLogDetail.status !== 'running'"
              style="margin-right: 10px; color: #909399; font-size: 12px;">
          ● 显示最终结果
        </span>
        <el-button v-if="isRealTimeMode" type="success" size="mini" icon="el-icon-refresh" @click="refreshRealTimeLog">
          刷新
        </el-button>
        <el-button 
          v-if="isRealTimeMode && currentLogDetail && currentLogDetail.status === 'running'" 
          type="danger" 
          size="mini" 
          icon="el-icon-close" 
          @click="stopTask"
        >
          停止
        </el-button>
      </div>
      <div style="clear: both;"></div>
    </div>

    <!-- 实时模式 -->
    <div v-if="isRealTimeMode">
      <!-- 任务状态信息 -->
      <el-card v-if="currentLogDetail" class="real-time-status-card">
        <div class="status-info-grid">
          <div class="status-item">
            <span class="status-label">任务状态:</span>
            <el-tag :type="getStatusType(currentLogDetail.status)" size="mini">
              {{ getStatusText(currentLogDetail.status) }}
            </el-tag>
          </div>
          <div class="status-item">
            <span class="status-label">开始时间:</span>
            <span class="status-value">{{ currentLogDetail.start_time || '-' }}</span>
          </div>
          <div class="status-item">
            <span class="status-label">结束时间:</span>
            <span class="status-value">{{ currentLogDetail.end_time || '-' }}</span>
          </div>
          <div class="status-item">
            <span class="status-label">运行时长:</span>
            <span class="status-value">{{ formatDuration(currentLogDetail.duration) }}</span>
          </div>
          <div class="status-item">
            <span class="status-label">退出码:</span>
            <span class="status-value">{{ currentLogDetail.exit_code !== null ? currentLogDetail.exit_code : '-' }}</span>
          </div>
          <div class="status-item">
            <span class="status-label">任务ID:</span>
            <span class="status-value">{{ currentLogDetail.task_id }}</span>
          </div>
        </div>
      </el-card>

      <div>
        <h4 style="margin-bottom: 8px;">实时日志输出:</h4>
        <el-input
          :value="currentLogContent"
          type="textarea"
          :rows="currentLogRows"
          readonly
          placeholder="等待日志输出..."
          class="log-textarea"
          style="font-family: 'Courier New', monospace; font-size: 14px; width: 100%; max-width: 100%;"
        />
      </div>
    </div>

    <!-- 历史模式 -->
    <div v-else>
      <el-table
        :data="currentTaskLogs"
        style="width: 100%"
        max-height="600"
      >
        <el-table-column prop="id" label="日志ID" width="80" />
        <el-table-column prop="start_time" label="开始时间" width="160" />
        <el-table-column prop="end_time" label="结束时间" width="160" />
        <el-table-column prop="duration" label="运行时长" width="100">
          <template slot-scope="{row}">
            {{ formatDuration(row.duration) }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template slot-scope="{row}">
            <el-tag :type="getStatusType(row.status)" size="mini">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="120">
          <template slot-scope="{row}">
            <el-button type="text" size="mini" @click="viewLogDetail(row)">
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'TaskLogDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    currentTask: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      isRealTimeMode: true,
      currentLogDetail: null,
      currentTaskLogs: [],
      realTimeLogTimer: null,
      realTimeTaskId: null
    }
  },
  computed: {
    currentLogContent() {
      return this.getCompleteLog(this.currentLogDetail)
    },
    currentLogRows() {
      return this.calculateTextareaRows(this.currentLogContent)
    }
  },
  watch: {
    visible(newVal) {
      if (newVal && this.currentTask) {
        this.initLogDialog()
      } else {
        this.stopRealTimeLog()
      }
    }
  },
  methods: {
    initLogDialog() {
      this.realTimeTaskId = this.currentTask.id
      this.isRealTimeMode = true

      if (this.currentTask.status === 'running') {
        this.startRealTimeLog(this.currentTask.id)
      } else {
        this.fetchLatestLogForRealTimeMode(this.currentTask.id)
      }
    },
    
    handleClose() {
      this.stopRealTimeLog()
      this.$emit('update:visible', false)
    },

    getCompleteLog(logDetail) {
      if (!logDetail) return '无日志数据'

      let completeLog = ''
      if (logDetail.output) {
        completeLog += logDetail.output
      }
      if (logDetail.error) {
        if (completeLog) completeLog += '\n\n=== 错误输出 ===\n'
        completeLog += logDetail.error
      }

      if (!completeLog && logDetail.status === 'running') {
        return '任务正在运行中，等待日志输出...\n\n提示：日志可能需要几秒钟才会显示'
      }

      return completeLog || '无日志输出'
    },

    calculateTextareaRows(content) {
      if (!content) return 8
      
      const lines = content.split('\n').length
      const minRows = 8
      const maxRows = 40
      
      return Math.max(minRows, Math.min(maxRows, lines + 2))
    },

    getStatusType(status) {
      const statusMap = {
        'pending': 'info',
        'running': 'warning', 
        'completed': 'success',
        'failed': 'danger'
      }
      return statusMap[status] || 'info'
    },

    getStatusText(status) {
      const statusMap = {
        'pending': '等待中',
        'running': '运行中',
        'completed': '已完成', 
        'failed': '失败'
      }
      return statusMap[status] || status
    },

    toggleRealTimeMode() {
      if (this.isRealTimeMode) {
        this.startRealTimeLog(this.realTimeTaskId)
      } else {
        this.stopRealTimeLog()
        this.fetchTaskLogs(this.realTimeTaskId)
      }
    },

    startRealTimeLog(taskId) {
      this.stopRealTimeLog()
      this.fetchLatestLog(taskId)
      
      this.realTimeLogTimer = setInterval(() => {
        this.fetchLatestLog(taskId)
      }, 2000)
    },

    stopRealTimeLog() {
      if (this.realTimeLogTimer) {
        clearInterval(this.realTimeLogTimer)
        this.realTimeLogTimer = null
      }
    },

    refreshRealTimeLog() {
      if (this.realTimeTaskId) {
        this.fetchLatestLog(this.realTimeTaskId)
      }
    },

    stopTask() {
      if (!this.currentTask) return
      
      this.$confirm('确认停止当前任务？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$emit('stop-task', this.currentTask.id)
      })
    },

    fetchLatestLog(taskId) {
      this.$emit('fetch-latest-log', taskId)
    },

    fetchLatestLogForRealTimeMode(taskId) {
      this.$emit('fetch-latest-log-for-realtime', taskId)
    },

    fetchTaskLogs(taskId) {
      this.$emit('fetch-task-logs', taskId)
    },

    viewLogDetail(log) {
      this.$emit('view-log-detail', log)
    },

    updateLogDetail(logDetail) {
      this.currentLogDetail = logDetail
    },

    updateTaskLogs(logs) {
      this.currentTaskLogs = logs
    },

    formatDuration(duration) {
      if (!duration && duration !== 0) return '-'

      // 确保duration是整数
      const intDuration = Math.round(duration)

      if (intDuration < 60) {
        return `${intDuration}秒`
      } else if (intDuration < 3600) {
        const minutes = Math.floor(intDuration / 60)
        const seconds = intDuration % 60
        return `${minutes}分${seconds}秒`
      } else {
        const hours = Math.floor(intDuration / 3600)
        const minutes = Math.floor((intDuration % 3600) / 60)
        const seconds = intDuration % 60
        return `${hours}时${minutes}分${seconds}秒`
      }
    }
  },

  beforeDestroy() {
    this.stopRealTimeLog()
  }
}
</script>

<style scoped>
.real-time-status-card {
  margin-bottom: 15px;
}

.real-time-status-card .el-card__body {
  padding: 15px;
}

.status-info-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
  align-items: center;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-label {
  font-weight: 500;
  color: #606266;
  white-space: nowrap;
  min-width: 60px;
}

.status-value {
  color: #303133;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
}

.log-textarea .el-textarea__inner {
  min-height: 200px !important;
  max-height: 75vh !important;
  resize: vertical !important;
  font-family: 'Courier New', 'Monaco', 'Menlo', monospace !important;
  font-size: 14px !important;
  line-height: 1.5 !important;
  overflow-y: auto !important;
  transition: height 0.3s ease !important;
}

.log-dialog .el-dialog {
  max-height: 94vh;
  max-width: 800px;
  display: flex;
  flex-direction: column;
}

.log-dialog .el-dialog__body {
  flex: 1;
  overflow: hidden;
  padding: 10px 15px;
  max-height: calc(94vh - 120px);
}
</style>
