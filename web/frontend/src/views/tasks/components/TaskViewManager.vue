<template>
  <div class="view-manager">
    <!-- 视图标签页 -->
    <el-tabs :value="currentViewId" @tab-click="handleViewChange" class="view-tabs">
      <el-tab-pane
        v-for="view in taskViews"
        :key="view.id"
        :label="getViewLabel(view)"
        :name="view.id.toString()"
      >
      </el-tab-pane>
      <el-tab-pane label="+" name="add" class="add-view-tab">
        <template slot="label">
          <el-button type="text" size="mini" @click="handleCreateView" style="color: #409EFF;">
            <i class="el-icon-plus"></i> 新建视图
          </el-button>
        </template>
      </el-tab-pane>
    </el-tabs>

    <!-- 当前视图的操作按钮 -->
    <div v-if="currentView && currentView.id !== 1" class="view-actions">
      <el-button type="text" size="mini" @click="handleEditView(currentView)">
        <i class="el-icon-edit"></i> 编辑视图
      </el-button>
      <el-button type="text" size="mini" @click="handleDeleteView(currentView)" style="color: #F56C6C;">
        <i class="el-icon-delete"></i> 删除视图
      </el-button>
    </div>

    <!-- 视图创建/编辑对话框 -->
    <el-dialog :title="viewDialogTitle" :visible.sync="viewDialogVisible" width="500px">
      <el-form ref="viewForm" :model="viewForm" :rules="viewRules" label-width="100px">
        <el-form-item label="视图名称" prop="name">
          <el-input v-model="viewForm.name" placeholder="请输入视图名称" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input v-model="viewForm.description" type="textarea" :rows="3" placeholder="请输入视图描述" />
        </el-form-item>
        <el-form-item label="筛选类型" prop="filter_type">
          <el-select v-model="viewForm.filter_type" placeholder="请选择筛选类型" style="width: 100%;">
            <el-option label="按脚本路径" value="path" />
            <el-option label="按任务状态" value="status" />
            <el-option label="按任务名称" value="name" />
          </el-select>
        </el-form-item>
        <el-form-item label="筛选值" prop="filter_value">
          <el-input 
            v-model="viewForm.filter_value" 
            :placeholder="getFilterPlaceholder()" 
          />
          <div style="font-size: 12px; color: #909399; margin-top: 4px;">
            {{ getFilterHint() }}
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="viewDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleViewSubmit">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'TaskViewManager',
  props: {
    taskViews: {
      type: Array,
      default: () => []
    },
    currentViewId: {
      type: [String, Number],
      default: null
    }
  },
  data() {
    return {
      viewDialogVisible: false,
      viewDialogTitle: '创建视图',
      isEditingView: false,
      editingViewId: null,
      viewForm: {
        name: '',
        description: '',
        filter_type: 'path',
        filter_value: ''
      },
      viewRules: {
        name: [
          { required: true, message: '请输入视图名称', trigger: 'blur' },
          { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
        ],
        filter_type: [
          { required: true, message: '请选择筛选类型', trigger: 'change' }
        ],
        filter_value: [
          { required: true, message: '请输入筛选值', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    currentView() {
      return this.taskViews.find(view => view.id.toString() === this.currentViewId?.toString())
    }
  },
  methods: {
    getViewLabel(view) {
      const count = this.getViewTaskCount(view.id)
      return `${view.name} ${count !== '-' ? `(${count})` : ''}`
    },

    getViewTaskCount(viewId) {
      // 这里可以实现一个简单的计数逻辑
      // 为了性能考虑，暂时返回 '-'，后续可以优化
      return '-'
    },

    handleViewChange(tab) {
      if (tab.name === 'add') {
        return
      }
      this.$emit('view-change', tab.name)
    },

    handleCreateView() {
      this.viewDialogTitle = '创建视图'
      this.isEditingView = false
      this.editingViewId = null
      this.resetViewForm()
      this.viewDialogVisible = true
    },

    handleEditView(view) {
      this.viewDialogTitle = '编辑视图'
      this.isEditingView = true
      this.editingViewId = view.id
      this.viewForm = {
        name: view.name,
        description: view.description || '',
        filter_type: view.filter_type || 'path',
        filter_value: view.filter_value || ''
      }
      this.viewDialogVisible = true
    },

    handleDeleteView(view) {
      this.$confirm(`确认删除视图 "${view.name}"？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$emit('delete-view', view.id)
      })
    },

    handleViewSubmit() {
      this.$refs.viewForm.validate((valid) => {
        if (valid) {
          if (this.isEditingView) {
            this.$emit('update-view', {
              id: this.editingViewId,
              ...this.viewForm
            })
          } else {
            this.$emit('create-view', this.viewForm)
          }
          this.viewDialogVisible = false
        }
      })
    },

    resetViewForm() {
      this.viewForm = {
        name: '',
        description: '',
        filter_type: 'path',
        filter_value: ''
      }
      if (this.$refs.viewForm) {
        this.$refs.viewForm.clearValidate()
      }
    },

    getFilterPlaceholder() {
      const placeholders = {
        'path': '例如：scripts/automation/',
        'status': '例如：running, completed, failed',
        'name': '例如：数据处理'
      }
      return placeholders[this.viewForm.filter_type] || '请输入筛选值'
    },

    getFilterHint() {
      const hints = {
        'path': '筛选包含指定路径的任务',
        'status': '筛选指定状态的任务，多个状态用逗号分隔',
        'name': '筛选包含指定名称的任务'
      }
      return hints[this.viewForm.filter_type] || ''
    }
  }
}
</script>

<style scoped>
.view-manager {
  margin-bottom: 20px;
}

.view-tabs {
  margin-bottom: 10px;
}

.view-tabs .el-tabs__header {
  margin-bottom: 10px;
}

.view-actions {
  text-align: right;
  margin-bottom: 10px;
}

.view-actions .el-button {
  margin-left: 10px;
}

.add-view-tab .el-button {
  padding: 0;
  border: none;
  background: none;
}

.add-view-tab .el-button:hover {
  background: none;
}
</style>
