<template>
  <el-dialog :title="dialogTitle" :visible="visible" width="600px" @close="handleClose">
    <el-form
      ref="dataForm"
      :rules="rules"
      :model="temp"
      label-position="left"
      label-width="120px"
      style="width: 100%; margin-left:10px;"
    >
      <el-form-item label="任务名称" prop="name">
        <el-input v-model="temp.name" placeholder="请输入任务名称" />
      </el-form-item>
      <el-form-item label="脚本路径" prop="script_path">
        <el-input v-model="temp.script_path" placeholder="请输入脚本路径，如：scripts/example.py" />
        <div style="font-size: 12px; color: #909399; margin-top: 4px;">
          相对于项目根目录的脚本路径
        </div>
      </el-form-item>
      <el-form-item label="Cron表达式" prop="cron_expression">
        <el-input v-model="temp.cron_expression" placeholder="请输入Cron表达式，如：0 */5 * * * *" />
        <div style="font-size: 12px; color: #909399; margin-top: 4px;">
          格式：秒 分 时 日 月 周，留空表示不定时执行
        </div>
      </el-form-item>
      <el-form-item label="描述">
        <el-input
          v-model="temp.description"
          :autosize="{ minRows: 2, maxRows: 4}"
          type="textarea"
          placeholder="请输入任务描述"
        />
      </el-form-item>
      <el-form-item label="启用状态">
        <el-switch v-model="temp.enabled" />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSubmit">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'TaskFormDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    dialogStatus: {
      type: String,
      default: 'create'
    },
    temp: {
      type: Object,
      default: () => ({
        id: undefined,
        name: '',
        script_path: '',
        cron_expression: '',
        description: '',
        enabled: true
      })
    }
  },
  computed: {
    dialogTitle() {
      return this.dialogStatus === 'create' ? '新增任务' : '编辑任务'
    }
  },
  data() {
    return {
      rules: {
        name: [
          { required: true, message: '请输入任务名称', trigger: 'blur' },
          { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' }
        ],
        script_path: [
          { required: true, message: '请输入脚本路径', trigger: 'blur' },
          {
            pattern: /^[\w\u4e00-\u9fa5_\-\/\.]+\.py$/,
            message: '请输入有效的Python脚本路径，如：scripts/example.py 或 scripts/投放自动化/获取数据.py',
            trigger: 'blur'
          }
        ]
      }
    }
  },
  methods: {
    handleClose() {
      this.$emit('update:visible', false)
      this.$emit('close')
    },
    
    handleSubmit() {
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          this.$emit('submit', { ...this.temp })
          this.handleClose()
        }
      })
    }
  }
}
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}
</style>
