<template>
  <div class="app-container">
    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧视图栏 -->
      <div class="view-sidebar">
        <div class="view-header">
          <span class="view-title">任务视图</span>
          <el-button
            type="text"
            icon="el-icon-plus"
            size="mini"
            @click="showCreateViewDialog"
            title="创建视图"
          />
        </div>

        <div class="view-list">
          <div
            v-for="view in taskViews"
            :key="view.id"
            class="view-item"
            :class="{ active: currentViewId === view.id }"
            @click="selectView(view.id)"
          >
            <div class="view-info">
              <span class="view-name">{{ view.name }}</span>
              <span class="view-count">({{ view.task_count || 0 }})</span>
            </div>
            <div class="view-actions" v-if="!view.is_default">
              <el-button
                type="text"
                icon="el-icon-edit"
                size="mini"
                @click.stop="showEditViewDialog(view)"
                title="编辑视图"
              />
              <el-button
                type="text"
                icon="el-icon-delete"
                size="mini"
                @click.stop="deleteView(view.id)"
                title="删除视图"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧任务列表 -->
      <div class="task-content">
        <div class="filter-container">
      <el-input
        v-model="listQuery.search"
        placeholder="搜索任务名称或描述"
        style="width: 200px;"
        class="filter-item"
        @keyup.enter.native="handleFilter"
      />
      <el-button
        v-waves
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleFilter"
      >
        搜索
      </el-button>
      <el-button
        class="filter-item"
        style="margin-left: 10px;"
        type="primary"
        icon="el-icon-plus"
        @click="handleCreateDialog"
      >
        新增任务
      </el-button>
    </div>

    <el-table
      :key="tableKey"
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%;"
    >
      <el-table-column label="ID" prop="id" align="center" width="80">
        <template slot-scope="{row}">
          <span>{{ row.id }}</span>
        </template>
      </el-table-column>
      
      <el-table-column label="任务名称" min-width="150px">
        <template slot-scope="{row}">
          <span class="link-type" @click="handleViewHistoryLogs(row)" title="点击查看历史日志">{{ row.name }}</span>
        </template>
      </el-table-column>
      
      <el-table-column label="描述" min-width="200px">
        <template slot-scope="{row}">
          <span>{{ row.description || '-' }}</span>
        </template>
      </el-table-column>
      
      <el-table-column label="类型" width="100px" align="center">
        <template slot-scope="{row}">
          <el-tag :type="getTaskTypeColor(row.task_type)">
            {{ getTaskTypeText(row.task_type) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="调度配置" min-width="200px">
        <template slot-scope="{row}">
          <div class="schedule-config">
            <div v-if="row.task_type === 'cron'" class="config-item">
              <span class="config-label">Cron:</span>
              <code class="config-value">{{ row.cron_expression || '-' }}</code>
            </div>
            <div v-else-if="row.task_type === 'interval'" class="config-item">
              <span class="config-label">间隔:</span>
              <span class="config-value">{{ formatInterval(row.interval_seconds) }}</span>
            </div>
            <div v-else-if="row.task_type === 'date'" class="config-item">
              <span class="config-label">执行时间:</span>
              <span class="config-value">{{ formatTime(row.run_date) }}</span>
            </div>
          </div>
        </template>
      </el-table-column>
      
      <el-table-column label="状态" width="100px" align="center">
        <template slot-scope="{row}">
          <el-tag :type="getStatusColor(row.status)">
            {{ getStatusText(row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column label="启用状态" width="100px" align="center">
        <template slot-scope="{row}">
          <el-switch
            v-model="row.enabled"
            @change="handleStatusChange(row)"
          />
        </template>
      </el-table-column>
      
      <el-table-column label="运行统计" width="120px" align="center">
        <template slot-scope="{row}">
          <div>
            <span style="color: #67C23A;">成功: {{ row.success_count }}</span><br>
            <span style="color: #F56C6C;">失败: {{ row.fail_count }}</span>
          </div>
        </template>
      </el-table-column>
      
      <el-table-column label="最后运行" width="150px" align="center">
        <template slot-scope="{row}">
          <span>{{ formatTime(row.last_run_time) }}</span>
        </template>
      </el-table-column>

      <el-table-column label="下次运行" width="150px" align="center">
        <template slot-scope="{row}">
          <span :class="getNextRunClass(row)">{{ formatTime(row.next_run_time) }}</span>
        </template>
      </el-table-column>
      
      <el-table-column label="操作" align="center" width="360px" class-name="small-padding fixed-width">
        <template slot-scope="{row}">
          <el-button
            :type="row.status === 'running' ? 'danger' : 'primary'"
            size="mini"
            @click="row.status === 'running' ? handleStopTask(row) : handleRunNow(row)"
          >
            {{ row.status === 'running' ? '停止' : '立即执行' }}
          </el-button>
          <el-button type="info" size="mini" @click="handleViewLogs(row)">
            查看日志
          </el-button>
          <el-button type="warning" size="mini" @click="handleEdit(row)">
            编辑
          </el-button>
          <el-button type="success" size="mini" @click="handleCopy(row)">
            复制
          </el-button>
          <el-button
            size="mini"
            type="danger"
            @click="handleDelete(row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.limit"
      @pagination="getList"
    />



    <!-- 编辑任务对话框 -->
    <el-dialog :title="currentTask ? '编辑任务' : '复制任务'" :visible.sync="editDialogVisible" width="800px">
      <el-form
        ref="editForm"
        :model="editForm"
        :rules="editRules"
        label-width="120px"
        v-loading="editLoading"
      >
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>基本信息</span>
          </div>

          <el-form-item label="任务名称" prop="name">
            <el-input
              v-model="editForm.name"
              placeholder="请输入任务名称"
              style="width: 400px;"
            />
          </el-form-item>

          <el-form-item label="任务描述" prop="description">
            <el-input
              v-model="editForm.description"
              type="textarea"
              :rows="3"
              placeholder="请输入任务描述"
              style="width: 400px;"
            />
          </el-form-item>

          <el-form-item label="脚本路径" prop="script_path">
            <el-input
              v-model="editForm.script_path"
              placeholder="请输入Python脚本的绝对路径"
              style="width: 400px;"
            />
          </el-form-item>
        </el-card>

        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>调度配置</span>
          </div>

          <el-form-item label="任务类型" prop="task_type">
            <el-radio-group v-model="editForm.task_type" @change="handleEditTaskTypeChange">
              <el-radio label="cron">Cron定时任务</el-radio>
              <el-radio label="interval">间隔任务</el-radio>
              <el-radio label="date">一次性任务</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item
            v-if="editForm.task_type === 'cron'"
            label="Cron表达式"
            prop="cron_expression"
          >
            <el-input
              v-model="editForm.cron_expression"
              placeholder="请输入Cron表达式，如: 0 */5 * * *"
              style="width: 400px;"
            />
          </el-form-item>

          <el-form-item
            v-if="editForm.task_type === 'interval'"
            label="间隔时间(秒)"
            prop="interval_seconds"
          >
            <el-input-number
              v-model="editForm.interval_seconds"
              :min="1"
              :max="86400"
              placeholder="请输入间隔秒数"
              style="width: 400px;"
            />
          </el-form-item>

          <el-form-item
            v-if="editForm.task_type === 'date'"
            label="执行时间"
            prop="run_date"
          >
            <el-date-picker
              v-model="editForm.run_date"
              type="datetime"
              placeholder="选择执行时间"
              style="width: 400px;"
            />
          </el-form-item>
        </el-card>

        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>任务设置</span>
          </div>

          <el-form-item label="启用状态">
            <el-switch v-model="editForm.enabled" />
          </el-form-item>
        </el-card>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="editDialogVisible = false">
          取消
        </el-button>
        <el-button type="primary" @click="confirmEdit" :loading="editLoading">
          保存修改
        </el-button>
      </div>
    </el-dialog>

    <!-- 创建任务对话框 -->
    <el-dialog title="创建任务" :visible.sync="createDialogVisible" width="800px">
      <el-form
        ref="createForm"
        :model="createForm"
        :rules="createRules"
        label-width="120px"
        v-loading="createLoading"
      >
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>基本信息</span>
          </div>

          <el-form-item label="任务名称" prop="name">
            <el-input
              v-model="createForm.name"
              placeholder="请输入任务名称"
              style="width: 400px;"
            />
          </el-form-item>

          <el-form-item label="任务描述" prop="description">
            <el-input
              v-model="createForm.description"
              type="textarea"
              :rows="3"
              placeholder="请输入任务描述"
              style="width: 400px;"
            />
          </el-form-item>

          <el-form-item label="脚本路径" prop="script_path">
            <el-input
              v-model="createForm.script_path"
              placeholder="请输入Python脚本路径（支持相对路径，如: scripts/test.py）"
              style="width: 400px;"
            />
            <div style="margin-top: 5px; color: #909399; font-size: 12px;">
              支持相对路径（基于backend目录）或绝对路径
            </div>
          </el-form-item>
        </el-card>

        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>调度配置</span>
          </div>

          <el-form-item label="任务类型" prop="task_type">
            <el-radio-group v-model="createForm.task_type" @change="handleCreateTaskTypeChange">
              <el-radio label="cron">Cron定时任务</el-radio>
              <el-radio label="interval">间隔任务</el-radio>
              <el-radio label="date">一次性任务</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item
            v-if="createForm.task_type === 'cron'"
            label="Cron表达式"
            prop="cron_expression"
          >
            <el-input
              v-model="createForm.cron_expression"
              placeholder="请输入Cron表达式，如: 0 */5 * * *"
              style="width: 400px;"
            />
            <div style="margin-top: 5px; color: #909399; font-size: 12px;">
              格式: 分 时 日 月 周，例如: 0 2 * * * (每天凌晨2点)
            </div>
          </el-form-item>

          <el-form-item
            v-if="createForm.task_type === 'interval'"
            label="间隔时间(秒)"
            prop="interval_seconds"
          >
            <el-input-number
              v-model="createForm.interval_seconds"
              :min="1"
              :max="86400"
              placeholder="请输入间隔秒数"
              style="width: 400px;"
            />
            <div style="margin-top: 5px; color: #909399; font-size: 12px;">
              1-86400秒，例如: 300 (每5分钟执行一次)
            </div>
          </el-form-item>

          <el-form-item
            v-if="createForm.task_type === 'date'"
            label="执行时间"
            prop="run_date"
          >
            <el-date-picker
              v-model="createForm.run_date"
              type="datetime"
              placeholder="选择执行时间"
              style="width: 400px;"
            />
          </el-form-item>
        </el-card>

        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>任务设置</span>
          </div>

          <el-form-item label="启用状态">
            <el-switch v-model="createForm.enabled" />
            <div style="margin-top: 5px; color: #909399; font-size: 12px;">
              创建后是否立即启用任务
            </div>
          </el-form-item>
        </el-card>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="createDialogVisible = false">
          取消
        </el-button>
        <el-button type="primary" @click="confirmCreate" :loading="createLoading">
          创建任务
        </el-button>
      </div>
    </el-dialog>

    <!-- 任务日志对话框 -->
    <el-dialog
      title="任务执行日志"
      :visible.sync="logDialogVisible"
      width="30%"
      top="3vh"
      @close="stopRealTimeLog"
      class="log-dialog"
    >
      <div slot="title" class="dialog-title">
        <span>任务执行日志</span>
        <div style="float: right;">
          <el-switch
            v-model="isRealTimeMode"
            active-text="实时模式"
            inactive-text="历史模式"
            @change="toggleRealTimeMode"
            style="margin-right: 10px;"
          />
          <span v-if="isRealTimeMode && currentLogDetail && currentLogDetail.status === 'running'"
                style="margin-right: 10px; color: #67C23A; font-size: 12px;">
            ● 实时更新中
          </span>
          <span v-if="isRealTimeMode && currentLogDetail && currentLogDetail.status !== 'running'"
                style="margin-right: 10px; color: #909399; font-size: 12px;">
            ● 显示最终结果
          </span>
          <el-button v-if="isRealTimeMode" type="success" size="mini" icon="el-icon-refresh" @click="refreshRealTimeLog">
            刷新
          </el-button>
          <el-button
            v-if="isRealTimeMode && currentLogDetail && currentLogDetail.status === 'running'"
            type="danger"
            size="mini"
            icon="el-icon-close"
            @click="stopTask"
          >
            停止
          </el-button>
        </div>
      </div>

      <div v-if="isRealTimeMode && currentLogDetail">
        <!-- 实时日志显示 -->
        <div style="margin-bottom: 15px;">
          <el-card class="real-time-status-card" style="margin-bottom: 0;">
            <div class="status-info">
              <div class="status-item">
                <span class="label">任务状态:</span>
                <el-tag :type="getStatusColor(currentLogDetail.status)" size="small">
                  <span class="status-indicator" :class="currentLogDetail.status"></span>
                  {{ getStatusText(currentLogDetail.status) }}
                  <span v-if="currentLogDetail.status !== 'running' && isRealTimeMode" style="margin-left: 5px;">
                    (已完成)
                  </span>
                </el-tag>
              </div>
              <div class="status-item">
                <span class="label">开始时间:</span>
                <span>{{ formatTime(currentLogDetail.start_time) }}</span>
              </div>
              <div class="status-item">
                <span class="label">运行时长:</span>
                <span class="duration">{{ getRealTimeDuration(currentLogDetail) }}</span>
              </div>
            </div>
          </el-card>
        </div>

        <div>
          <h4 style="margin-bottom: 8px;">实时日志输出:</h4>
          <el-input
            :value="currentLogContent"
            type="textarea"
            :rows="currentLogRows"
            readonly
            placeholder="等待日志输出..."
            class="log-textarea"
            style="font-family: 'Courier New', monospace; font-size: 14px; width: 100%; max-width: 100%;"
          />
        </div>
      </div>

      <div v-else-if="currentTaskLogs.length > 0">
        <!-- 历史日志列表 -->
        <el-table
          :data="currentTaskLogs"
          style="width: 100%"
          max-height="600"
        >
          <el-table-column prop="id" label="ID" width="80" align="center" />
          <el-table-column prop="start_time" label="开始时间" width="160" align="center">
            <template slot-scope="{row}">
              {{ formatTime(row.start_time) }}
            </template>
          </el-table-column>
          <el-table-column prop="end_time" label="结束时间" width="160" align="center">
            <template slot-scope="{row}">
              {{ formatTime(row.end_time) }}
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100" align="center">
            <template slot-scope="{row}">
              <el-tag :type="getStatusColor(row.status)" size="small">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="exit_code" label="退出码" width="80" align="center">
            <template slot-scope="{row}">
              <span :style="{ color: row.exit_code === 0 ? '#67C23A' : '#F56C6C' }">
                {{ row.exit_code !== null ? row.exit_code : '-' }}
              </span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120" align="center">
            <template slot-scope="{row}">
              <el-button type="primary" size="mini" @click="viewLogDetail(row)">
                查看详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div v-else style="text-align: center; padding: 20px; color: #909399;">
        暂无执行日志
      </div>
    </el-dialog>

    <!-- 日志详情对话框 -->
    <el-dialog
      title="执行日志详情"
      :visible.sync="logDetailVisible"
      width="40%"
      top="3vh"
      class="log-detail-dialog"
    >
      <div v-if="currentLogDetail">
        <el-descriptions :column="3" border style="margin-bottom: 15px;" size="small">
          <el-descriptions-item label="执行状态">
            <el-tag :type="getStatusColor(currentLogDetail.status)" size="small">
              {{ getStatusText(currentLogDetail.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="退出码">
            <span :style="{ color: currentLogDetail.exit_code === 0 ? '#67C23A' : '#F56C6C' }">
              {{ currentLogDetail.exit_code !== null ? currentLogDetail.exit_code : '-' }}
            </span>
          </el-descriptions-item>
          <el-descriptions-item label="执行时长">
            {{ formatDuration(currentLogDetail.duration) }}
          </el-descriptions-item>
          <el-descriptions-item label="开始时间">
            {{ formatTime(currentLogDetail.start_time) }}
          </el-descriptions-item>
          <el-descriptions-item label="结束时间" :span="2">
            {{ formatTime(currentLogDetail.end_time) }}
          </el-descriptions-item>
        </el-descriptions>

        <div style="margin-top: 10px;">
          <h4 style="margin-bottom: 8px;">完整日志输出:</h4>
          <el-input
            :value="currentLogContent"
            type="textarea"
            :rows="currentLogRows"
            readonly
            placeholder="无日志输出"
            class="log-textarea"
            style="font-family: 'Courier New', monospace; font-size: 14px; width: 100%; max-width: 100%;"
          />
        </div>
      </div>
    </el-dialog>

    <!-- 创建/编辑视图对话框 -->
    <el-dialog
      :title="viewDialogTitle"
      :visible.sync="viewDialogVisible"
      width="500px"
      @close="resetViewForm"
    >
      <el-form ref="viewForm" :model="viewForm" :rules="viewRules" label-width="100px">
        <el-form-item label="视图名称" prop="name">
          <el-input v-model="viewForm.name" placeholder="请输入视图名称" />
        </el-form-item>

        <el-form-item label="描述" prop="description">
          <el-input
            v-model="viewForm.description"
            type="textarea"
            :rows="2"
            placeholder="请输入视图描述（可选）"
          />
        </el-form-item>

        <el-form-item label="筛选类型" prop="filter_type">
          <el-select v-model="viewForm.filter_type" placeholder="请选择筛选类型">
            <el-option label="路径包含" value="path" />
            <el-option label="名称包含" value="name" />
          </el-select>
        </el-form-item>

        <el-form-item label="筛选值" prop="filter_value">
          <el-input
            v-model="viewForm.filter_value"
            placeholder="请输入筛选值，如：投放自动化"
          />
          <div class="form-tip">
            <span v-if="viewForm.filter_type === 'path'">
              示例：输入 "投放自动化" 将显示路径包含此文字的任务
            </span>
            <span v-if="viewForm.filter_type === 'name'">
              示例：输入 "筛选" 将显示名称包含此文字的任务
            </span>
          </div>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="viewDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitViewForm">确定</el-button>
      </div>
    </el-dialog>
      </div> <!-- task-content -->
    </div> <!-- main-content -->
  </div>
</template>

<script>
import { getTaskList, deleteTask, runTaskNow, updateTask, getTask, createTask, getTaskLogs, getLatestTaskLog, getTaskStatus, getTaskViews, createTaskView, updateTaskView, deleteTaskView, getTasksByView } from '@/api/tasks'
import waves from '@/directive/waves'
import Pagination from '@/components/Pagination'

export default {
  name: 'TaskList',
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      tableKey: 0,
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        limit: 20,
        search: undefined
      },
      // 视图管理相关
      taskViews: [],
      currentViewId: null,
      viewDialogVisible: false,
      viewDialogTitle: '创建视图',
      isEditingView: false,
      editingViewId: null,
      viewForm: {
        name: '',
        description: '',
        filter_type: 'path',
        filter_value: ''
      },
      viewRules: {
        name: [
          { required: true, message: '请输入视图名称', trigger: 'blur' },
          { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
        ],
        filter_type: [
          { required: true, message: '请选择筛选类型', trigger: 'change' }
        ]
      },
      currentTask: {},
      editDialogVisible: false,
      editLoading: false,
      editForm: {
        name: '',
        description: '',
        script_path: '',
        task_type: 'cron',
        cron_expression: '',
        interval_seconds: null,
        run_date: null,
        enabled: true
      },
      editRules: {
        name: [
          { required: true, message: '请输入任务名称', trigger: 'blur' }
        ],
        script_path: [
          { required: true, message: '请输入脚本路径', trigger: 'blur' }
        ],
        task_type: [
          { required: true, message: '请选择任务类型', trigger: 'change' }
        ],
        cron_expression: [
          { required: true, message: '请输入Cron表达式', trigger: 'blur' }
        ],
        interval_seconds: [
          { required: true, message: '请输入间隔秒数', trigger: 'blur' }
        ],
        run_date: [
          { required: true, message: '请选择执行时间', trigger: 'change' }
        ]
      },
      createDialogVisible: false,
      createLoading: false,
      createForm: {
        name: '',
        description: '',
        script_path: '',
        task_type: 'cron',
        cron_expression: '',
        interval_seconds: null,
        run_date: null,
        enabled: true
      },
      createRules: {
        name: [
          { required: true, message: '请输入任务名称', trigger: 'blur' }
        ],
        script_path: [
          { required: true, message: '请输入脚本路径', trigger: 'blur' }
        ],
        task_type: [
          { required: true, message: '请选择任务类型', trigger: 'change' }
        ],
        cron_expression: [
          { required: true, message: '请输入Cron表达式', trigger: 'blur' }
        ],
        interval_seconds: [
          { required: true, message: '请输入间隔秒数', trigger: 'blur' }
        ],
        run_date: [
          { required: true, message: '请选择执行时间', trigger: 'change' }
        ]
      },
      logDialogVisible: false,
      logDetailVisible: false,
      currentTaskLogs: [],
      currentLogDetail: null,
      realTimeLogTimer: null,
      isRealTimeMode: false,
      realTimeTaskId: null,
      logFontSize: '14' // 默认字体大小
    }
  },
  computed: {
    // 计算当前日志内容
    currentLogContent() {
      return this.getCompleteLog(this.currentLogDetail)
    },
    // 计算当前日志应该显示的行数
    currentLogRows() {
      return this.calculateTextareaRows(this.currentLogContent)
    }
  },
  created() {
    this.getTaskViews() // 先加载视图列表
  },
  beforeDestroy() {
    // 组件销毁前清理定时器
    this.stopRealTimeLog()
  },
  methods: {
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    handleCreate() {
      this.$router.push('/tasks/create')
    },
    handleCreateDialog() {
      // 重置表单
      this.createForm = {
        name: '',
        description: '',
        script_path: '',
        task_type: 'cron',
        cron_expression: '',
        interval_seconds: null,
        run_date: null,
        enabled: true
      }
      this.createDialogVisible = true
    },
    handleUpdate(row) {
      this.$router.push(`/tasks/edit/${row.id}`)
    },
    handleEdit(row) {
      this.currentTask = row
      this.fetchTaskForEdit(row.id)
    },
    handleCopy(row) {
      // 复制任务并打开编辑弹窗
      this.editForm = {
        name: `${row.name} - 副本`,
        description: row.description,
        script_path: row.script_path,
        task_type: row.task_type,
        cron_expression: row.cron_expression,
        interval_seconds: row.interval_seconds,
        run_date: row.run_date ? new Date(row.run_date) : null,
        enabled: true // 默认启用复制的任务
      }

      // 设置为创建模式
      this.currentTask = null
      this.editDialogVisible = true
    },
    fetchTaskForEdit(taskId) {
      this.editLoading = true
      getTask(taskId).then(response => {
        if (response.code === 20000) {
          this.editForm = { ...response.data }



          // 处理日期格式
          if (this.editForm.run_date) {
            this.editForm.run_date = new Date(this.editForm.run_date)
          }

          this.editDialogVisible = true
        } else {
          this.$message.error(response.message || '获取任务详情失败')
        }
        this.editLoading = false
      }).catch(() => {
        this.editLoading = false
        this.$message.error('获取任务详情失败')
      })
    },
    handleEditTaskTypeChange() {
      // 清空其他类型的配置
      if (this.editForm.task_type !== 'cron') {
        this.editForm.cron_expression = ''
      }
      if (this.editForm.task_type !== 'interval') {
        this.editForm.interval_seconds = null
      }
      if (this.editForm.task_type !== 'date') {
        this.editForm.run_date = null
      }
    },

    confirmEdit() {
      this.$refs.editForm.validate((valid) => {
        if (valid) {
          this.editLoading = true
          const formData = { ...this.editForm }

          // 根据任务类型清理不需要的字段
          if (formData.task_type !== 'cron') {
            delete formData.cron_expression
          }
          if (formData.task_type !== 'interval') {
            delete formData.interval_seconds
          }
          if (formData.task_type !== 'date') {
            delete formData.run_date
          }

          if (this.currentTask) {
            // 编辑模式
            updateTask(this.currentTask.id, formData).then(response => {
              if (response.code === 20000) {
                this.$message.success('更新任务成功')
                this.editDialogVisible = false
                this.getList()
              } else {
                this.$message.error(response.message || '更新任务失败')
              }
              this.editLoading = false
            }).catch(() => {
              this.$message.error('更新任务失败')
              this.editLoading = false
            })
          } else {
            // 复制创建模式
            createTask(formData).then(response => {
              if (response.code === 20000) {
                this.$message.success('复制任务成功')
                this.editDialogVisible = false
                this.getList()
              } else {
                this.$message.error(response.message || '复制任务失败')
              }
              this.editLoading = false
            }).catch(() => {
              this.$message.error('复制任务失败')
              this.editLoading = false
            })
          }
        }
      })
    },
    handleCreateTaskTypeChange() {
      // 清空其他类型的配置
      if (this.createForm.task_type !== 'cron') {
        this.createForm.cron_expression = ''
      }
      if (this.createForm.task_type !== 'interval') {
        this.createForm.interval_seconds = null
      }
      if (this.createForm.task_type !== 'date') {
        this.createForm.run_date = null
      }
    },

    confirmCreate() {
      this.$refs.createForm.validate((valid) => {
        if (valid) {
          this.createLoading = true
          const formData = { ...this.createForm }

          // 根据任务类型清理不需要的字段
          if (formData.task_type !== 'cron') {
            delete formData.cron_expression
          }
          if (formData.task_type !== 'interval') {
            delete formData.interval_seconds
          }
          if (formData.task_type !== 'date') {
            delete formData.run_date
          }

          createTask(formData).then(response => {
            if (response.code === 20000) {
              this.$message.success('创建任务成功')
              this.createDialogVisible = false
              this.getList()
            } else {
              this.$message.error(response.message || '创建任务失败')
            }
            this.createLoading = false
          }).catch(() => {
            this.$message.error('创建任务失败')
            this.createLoading = false
          })
        }
      })
    },
    handleViewLogs(row) {
      this.currentTask = row
      this.realTimeTaskId = row.id

      // 默认开启实时模式开关
      this.isRealTimeMode = true

      // 检查任务是否正在运行，如果是则直接显示实时日志
      if (row.status === 'running') {
        this.startRealTimeLog(row.id)
        this.logDialogVisible = true
      } else {
        // 如果任务不在运行，获取最后一次日志并以实时模式显示
        this.fetchLatestLogForRealTimeMode(row.id)
      }
    },
    fetchTaskLogs(taskId) {
      getTaskLogs(taskId, { page: 1, limit: 50 }).then(response => {
        if (response.code === 20000) {
          this.currentTaskLogs = response.data.items || []
          this.logDialogVisible = true
        } else {
          this.$message.error(response.message || '获取任务日志失败')
        }
      }).catch(() => {
        this.$message.error('获取任务日志失败')
      })
    },
    fetchLatestLog(taskId) {
      // 获取最新的日志记录并直接显示详情
      getLatestTaskLog(taskId).then(response => {
        if (response.code === 20000 && response.data) {
          this.currentLogDetail = response.data
          this.isRealTimeMode = true // 设置为实时模式以显示日志详情
          this.logDialogVisible = true
        } else {
          // 如果没有日志记录，显示历史日志列表
          this.isRealTimeMode = false
          this.fetchTaskLogs(taskId)
        }
      }).catch(() => {
        // 出错时显示历史日志列表
        this.isRealTimeMode = false
        this.fetchTaskLogs(taskId)
      })
    },
    fetchLatestLogForRealTimeMode(taskId) {
      // 专门为实时模式获取最新日志
      getLatestTaskLog(taskId).then(response => {
        if (response.code === 20000 && response.data) {
          this.currentLogDetail = response.data
          this.logDialogVisible = true
        } else {
          // 如果没有日志记录，创建一个空的日志对象
          this.currentLogDetail = {
            id: null,
            task_id: taskId,
            status: 'pending',
            start_time: null,
            end_time: null,
            duration: null,
            exit_code: null,
            output: '',
            error: ''
          }
          this.logDialogVisible = true
        }
      }).catch(() => {
        // 出错时也创建空日志对象
        this.currentLogDetail = {
          id: null,
          task_id: taskId,
          status: 'failed',
          start_time: null,
          end_time: null,
          duration: null,
          exit_code: null,
          output: '',
          error: '获取日志失败'
        }
        this.logDialogVisible = true
      })
    },
    handleViewHistoryLogs(row) {
      // 点击任务名称查看历史日志列表
      this.currentTask = row
      this.realTimeTaskId = row.id
      this.isRealTimeMode = false
      this.fetchTaskLogs(row.id)
    },
    viewLogDetail(logItem) {
      this.currentLogDetail = logItem
      this.logDetailVisible = true
    },
    getCompleteLog(logDetail) {
      if (!logDetail) return '无日志数据'

      let completeLog = ''
      if (logDetail.output) {
        completeLog += logDetail.output
      }
      if (logDetail.error) {
        if (completeLog) completeLog += '\n\n=== 错误输出 ===\n'
        completeLog += logDetail.error
      }

      // 如果任务正在运行但还没有输出
      if (!completeLog && logDetail.status === 'running') {
        return '任务正在运行中，等待日志输出...\n\n提示：日志可能需要几秒钟才会显示'
      }

      return completeLog || '无日志输出'
    },
    // 计算文本框应该显示的行数
    calculateTextareaRows(content) {
      if (!content) return 8 // 默认最小行数

      const lines = content.split('\n').length
      const minRows = 8   // 最小行数
      const maxRows = 40  // 最大行数

      // 根据内容行数动态调整，但限制在最小和最大值之间
      return Math.max(minRows, Math.min(maxRows, lines + 2))
    },
    formatDuration(duration) {
      if (!duration) return '-'
      if (duration < 1) {
        return `${Math.round(duration * 1000)}ms`
      }
      return `${duration.toFixed(2)}s`
    },
    toggleRealTimeMode(enabled) {
      if (enabled && this.realTimeTaskId) {
        this.startRealTimeLog(this.realTimeTaskId)
      } else {
        this.stopRealTimeLog()
        if (this.realTimeTaskId) {
          this.fetchTaskLogs(this.realTimeTaskId)
        }
      }
    },
    startRealTimeLog(taskId) {
      this.stopRealTimeLog() // 先停止之前的定时器

      // 立即获取一次最新日志
      this.fetchLatestLog(taskId)

      // 设置定时器，每2秒获取一次最新日志
      this.realTimeLogTimer = setInterval(() => {
        this.fetchLatestLog(taskId)
      }, 2000)
    },
    stopRealTimeLog() {
      if (this.realTimeLogTimer) {
        clearInterval(this.realTimeLogTimer)
        this.realTimeLogTimer = null
      }
    },
    fetchLatestLog(taskId) {
      console.log('获取最新日志，任务ID:', taskId)
      getLatestTaskLog(taskId).then(response => {
        console.log('最新日志响应:', response)
        if (response.code === 20000) {
          if (response.data) {
            this.currentLogDetail = response.data
            this.logDialogVisible = true
            console.log('更新日志详情:', this.currentLogDetail)

            // 如果任务已完成，只停止实时更新，但保持实时模式显示最终结果
            if (response.data.status !== 'running') {
              console.log('任务已完成，停止实时更新但保持实时模式显示')
              this.stopRealTimeLog()
              // 刷新任务列表以更新状态
              this.getList()
              // 不关闭实时模式，让用户看到最终的完整日志
            }
          } else {
            console.log('暂无日志数据')
            // 如果没有日志数据，显示提示
            this.currentLogDetail = {
              status: 'pending',
              start_time: null,
              output: '任务尚未开始执行或暂无日志输出...',
              error: ''
            }
            this.logDialogVisible = true
          }
        } else {
          console.error('获取日志失败:', response.message)
          this.$message.error(response.message || '获取日志失败')
        }
      }).catch(error => {
        console.error('获取日志异常:', error)
        this.$message.error('获取日志失败')
        // 获取失败时停止实时更新
        this.stopRealTimeLog()
      })
    },
    refreshRealTimeLog() {
      if (this.realTimeTaskId) {
        this.fetchLatestLog(this.realTimeTaskId)
      }
    },
    stopTask() {
      if (!this.currentTask) return

      this.$confirm('确认停止当前任务？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$request({
          url: `/vue-element-admin/tasks/${this.currentTask.id}/stop`,
          method: 'post'
        }).then(response => {
          if (response.code === 20000) {
            this.$message.success('任务已停止')
            // 刷新任务状态
            this.refreshRealTimeLog()
            this.getList()
          } else {
            this.$message.error(response.message || '停止任务失败')
          }
        }).catch(error => {
          console.error('停止任务失败:', error)
          this.$message.error('停止任务失败')
        })
      })
    },
    handleStopTask(row) {
      this.$confirm(`确认停止任务 "${row.name}"？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$request({
          url: `/vue-element-admin/tasks/${row.id}/stop`,
          method: 'post'
        }).then(response => {
          if (response.code === 20000) {
            this.$message.success('任务已停止')
            // 刷新任务列表
            this.getList()
          } else {
            this.$message.error(response.message || '停止任务失败')
          }
        }).catch(error => {
          console.error('停止任务失败:', error)
          this.$message.error('停止任务失败')
        })
      })
    },
    getRealTimeDuration(logDetail) {
      if (!logDetail || !logDetail.start_time) return '-'

      const startTime = new Date(logDetail.start_time)
      let endTime, duration, prefix = ''

      if (logDetail.end_time) {
        // 任务已完成，显示总执行时长
        endTime = new Date(logDetail.end_time)
        prefix = '总时长: '
      } else {
        // 任务正在运行，显示当前运行时长
        endTime = new Date()
        prefix = '已运行: '
      }

      duration = (endTime - startTime) / 1000

      if (duration < 60) {
        return `${prefix}${duration.toFixed(1)}秒`
      } else if (duration < 3600) {
        return `${prefix}${Math.floor(duration / 60)}分${Math.floor(duration % 60)}秒`
      } else {
        const hours = Math.floor(duration / 3600)
        const minutes = Math.floor((duration % 3600) / 60)
        return `${prefix}${hours}小时${minutes}分钟`
      }
    },
    handleRunNow(row) {
      this.$confirm(`确定要立即执行任务"${row.name}"吗？`, '确认执行', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }).then(() => {
        runTaskNow(row.id, {}).then(response => {
          if (response.code === 20000) {
            this.$message.success('任务已开始执行')
            this.getList()
          } else {
            this.$message.error(response.message || '执行任务失败')
          }
        }).catch(() => {
          this.$message.error('执行任务失败')
        })
      })
    },
    handleStatusChange(row) {
      const action = row.enabled ? '启用' : '禁用'
      // 传递完整的任务信息，只更新enabled字段
      const updateData = {
        name: row.name,
        description: row.description,
        script_path: row.script_path,
        task_type: row.task_type,
        cron_expression: row.cron_expression,
        interval_seconds: row.interval_seconds,
        run_date: row.run_date,
        enabled: row.enabled
      }

      updateTask(row.id, updateData).then(response => {
        if (response.code === 20000) {
          this.$message.success(`${action}任务成功`)
          this.getList()
        } else {
          this.$message.error(response.message || `${action}任务失败`)
          row.enabled = !row.enabled // 回滚状态
        }
      }).catch(() => {
        row.enabled = !row.enabled // 回滚状态
      })
    },
    handleDelete(row) {
      this.$confirm('此操作将永久删除该任务, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteTask(row.id).then(response => {
          if (response.code === 20000) {
            this.$message.success('删除成功')
            this.getList()
          } else {
            this.$message.error(response.message || '删除失败')
          }
        })
      })
    },
    getTaskTypeText(type) {
      const typeMap = {
        'cron': 'Cron',
        'interval': '间隔',
        'date': '一次性'
      }
      return typeMap[type] || type
    },
    getTaskTypeColor(type) {
      const colorMap = {
        'cron': 'success',
        'interval': 'warning',
        'date': 'info'
      }
      return colorMap[type] || ''
    },
    getStatusText(status) {
      const statusMap = {
        'pending': '等待中',
        'running': '运行中',
        'success': '成功',
        'failed': '失败',
        'paused': '暂停'
      }
      return statusMap[status] || status
    },
    getStatusColor(status) {
      const colorMap = {
        'pending': 'info',
        'running': 'warning',
        'success': 'success',
        'failed': 'danger',
        'paused': 'info'
      }
      return colorMap[status] || ''
    },
    formatTime(timeStr) {
      if (!timeStr) return '-'
      return new Date(timeStr).toLocaleString()
    },
    formatInterval(seconds) {
      if (!seconds) return '-'
      if (seconds < 60) {
        return `${seconds}秒`
      } else if (seconds < 3600) {
        const minutes = Math.floor(seconds / 60)
        const remainingSeconds = seconds % 60
        return remainingSeconds > 0 ? `${minutes}分${remainingSeconds}秒` : `${minutes}分钟`
      } else if (seconds < 86400) {
        const hours = Math.floor(seconds / 3600)
        const minutes = Math.floor((seconds % 3600) / 60)
        return minutes > 0 ? `${hours}小时${minutes}分钟` : `${hours}小时`
      } else {
        const days = Math.floor(seconds / 86400)
        const hours = Math.floor((seconds % 86400) / 3600)
        return hours > 0 ? `${days}天${hours}小时` : `${days}天`
      }
    },
    getNextRunClass(row) {
      if (!row.next_run_time || !row.enabled) {
        return 'next-run-disabled'
      }
      const nextTime = new Date(row.next_run_time)
      const now = new Date()
      const diffMinutes = (nextTime - now) / (1000 * 60)

      if (diffMinutes < 0) {
        return 'next-run-overdue'
      } else if (diffMinutes < 30) {
        return 'next-run-soon'
      } else {
        return 'next-run-normal'
      }
    },

    // ==================== 视图管理方法 ====================

    // 获取任务视图列表
    getTaskViews() {
      getTaskViews().then(response => {
        if (response.code === 20000) {
          this.taskViews = response.data
          // 如果没有选中的视图，默认选择第一个（全部任务）
          if (!this.currentViewId && this.taskViews.length > 0) {
            this.currentViewId = this.taskViews[0].id
          }
          // 加载视图后，获取任务列表
          this.getList()
        } else {
          this.$message.error(response.message || '获取视图列表失败')
        }
      }).catch(error => {
        console.error('获取视图列表失败:', error)
        this.$message.error('获取视图列表失败')
      })
    },

    // 选择视图
    selectView(viewId) {
      this.currentViewId = viewId
      this.listQuery.page = 1 // 重置页码
      this.getList()
    },

    // 根据视图获取任务列表
    getList() {
      this.listLoading = true

      // 如果选择了特定视图，使用视图API
      if (this.currentViewId) {
        getTasksByView(this.currentViewId, {
          page: this.listQuery.page,
          limit: this.listQuery.limit
        }).then(response => {
          if (response.code === 20000) {
            this.list = response.data.items
            this.total = response.data.total
          } else {
            this.$message.error(response.message || '获取任务列表失败')
          }
          this.listLoading = false
        }).catch(error => {
          console.error('获取任务列表失败:', error)
          this.$message.error('获取任务列表失败')
          this.listLoading = false
        })
      } else {
        // 使用原有的API
        getTaskList(this.listQuery).then(response => {
          if (response.code === 20000) {
            this.list = response.data.items
            this.total = response.data.total
          } else {
            this.$message.error(response.message || '获取任务列表失败')
          }
          this.listLoading = false
        }).catch(error => {
          console.error('获取任务列表失败:', error)
          this.$message.error('获取任务列表失败')
          this.listLoading = false
        })
      }
    },

    // 获取视图中的任务数量
    getViewTaskCount(viewId) {
      // 这里可以实现一个简单的计数逻辑
      // 为了性能考虑，暂时返回 '-'，后续可以优化
      return '-'
    },

    // 显示创建视图对话框
    showCreateViewDialog() {
      this.viewDialogTitle = '创建视图'
      this.isEditingView = false
      this.editingViewId = null
      this.resetViewForm()
      this.viewDialogVisible = true
    },

    // 显示编辑视图对话框
    showEditViewDialog(view) {
      this.viewDialogTitle = '编辑视图'
      this.isEditingView = true
      this.editingViewId = view.id
      this.viewForm = {
        name: view.name,
        description: view.description || '',
        filter_type: view.filter_type,
        filter_value: view.filter_value || ''
      }
      this.viewDialogVisible = true
    },

    // 重置视图表单
    resetViewForm() {
      this.viewForm = {
        name: '',
        description: '',
        filter_type: 'path',
        filter_value: ''
      }
      if (this.$refs.viewForm) {
        this.$refs.viewForm.clearValidate()
      }
    },

    // 提交视图表单
    submitViewForm() {
      this.$refs.viewForm.validate((valid) => {
        if (valid) {
          if (this.isEditingView) {
            this.updateView()
          } else {
            this.createView()
          }
        }
      })
    },

    // 创建视图
    createView() {
      createTaskView(this.viewForm).then(response => {
        if (response.code === 20000) {
          this.$message.success('创建视图成功')
          this.viewDialogVisible = false
          this.getTaskViews() // 刷新视图列表
        } else {
          this.$message.error(response.message || '创建视图失败')
        }
      }).catch(error => {
        console.error('创建视图失败:', error)
        this.$message.error('创建视图失败')
      })
    },

    // 更新视图
    updateView() {
      updateTaskView(this.editingViewId, this.viewForm).then(response => {
        if (response.code === 20000) {
          this.$message.success('更新视图成功')
          this.viewDialogVisible = false
          this.getTaskViews() // 刷新视图列表
        } else {
          this.$message.error(response.message || '更新视图失败')
        }
      }).catch(error => {
        console.error('更新视图失败:', error)
        this.$message.error('更新视图失败')
      })
    },

    // 删除视图
    deleteView(viewId) {
      this.$confirm('确定要删除这个视图吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteTaskView(viewId).then(response => {
          if (response.code === 20000) {
            this.$message.success('删除视图成功')
            // 如果删除的是当前选中的视图，切换到默认视图
            if (this.currentViewId === viewId) {
              const defaultView = this.taskViews.find(v => v.is_default)
              if (defaultView) {
                this.currentViewId = defaultView.id
              }
            }
            this.getTaskViews() // 刷新视图列表
            this.getList() // 刷新任务列表
          } else {
            this.$message.error(response.message || '删除视图失败')
          }
        }).catch(error => {
          console.error('删除视图失败:', error)
          this.$message.error('删除视图失败')
        })
      }).catch(() => {
        // 用户取消删除
      })
    }
  }
}
</script>

<style scoped>
.dialog-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.dialog-title .el-switch {
  margin-left: auto;
}

/* 实时状态卡片 */
.real-time-status-card {
  margin-bottom: 15px;
}

.real-time-status-card .el-card__body {
  padding: 15px;
}

.status-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 15px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-item .label {
  font-weight: 500;
  color: #606266;
  min-width: 70px;
}

.status-item .duration {
  font-weight: 600;
  color: #409eff;
}

/* 实时日志样式 */
.real-time-log {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 10px;
  background-color: #f5f7fa;
}

/* 优化弹窗内容间距 */
.el-dialog__body {
  padding: 20px 20px 15px 20px;
}

/* 优化描述列表样式 */
.el-descriptions--small .el-descriptions__body .el-descriptions__table .el-descriptions__cell {
  padding: 8px 10px;
}

/* 日志弹窗样式优化 */
.log-dialog .el-dialog {
  max-height: 94vh;
  max-width: 800px;
  display: flex;
  flex-direction: column;
}

.log-dialog .el-dialog__body {
  flex: 1;
  overflow: hidden;
  padding: 10px 15px;
  max-height: calc(94vh - 120px);
}

.log-detail-dialog .el-dialog {
  max-height: 94vh;
  max-width: 700px;
  display: flex;
  flex-direction: column;
}

.log-detail-dialog .el-dialog__body {
  flex: 1;
  overflow: hidden;
  padding: 10px 15px;
  max-height: calc(94vh - 120px);
}

/* 优化日志文本框样式 */
.el-textarea__inner {
  resize: vertical !important;
  min-height: 100px !important;
  max-height: 200vh !important;
}

/* 专门针对日志文本框的样式 */
.log-textarea .el-textarea__inner {
  min-height: 200px !important;    /* 日志文本框的最小高度 */
  max-height: 75vh !important;     /* 日志文本框的最大高度 */
  resize: vertical !important;     /* 允许垂直调整大小 */
  font-family: 'Courier New', 'Monaco', 'Menlo', monospace !important;
  font-size: 14px !important;      /* 调整字体大小 */
  line-height: 1.5 !important;
  overflow-y: auto !important;     /* 内容超出时显示滚动条 */
  transition: height 0.3s ease !important; /* 高度变化时的平滑过渡 */
}

.real-time-log .el-textarea__inner {
  background-color: #2d3748;
  color: #e2e8f0;
  border: none;
  font-family: 'Courier New', 'Monaco', 'Menlo', monospace;
  font-size: 14px;
  line-height: 1.4;
}

/* 状态指示器 */
.status-indicator {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 5px;
}

.status-indicator.running {
  background-color: #67c23a;
  animation: pulse 1.5s infinite;
}

.status-indicator.success {
  background-color: #67c23a;
}

.status-indicator.failed {
  background-color: #f56c6c;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}
</style>

<style scoped>
.link-type {
  color: #409EFF;
  cursor: pointer;
}

.link-type:hover {
  color: #66b1ff;
}

.box-card {
  margin-bottom: 20px;
}

.dialog-footer {
  text-align: right;
}

.schedule-config {
  font-size: 12px;
}

.config-item {
  margin-bottom: 4px;
}

.config-label {
  color: #909399;
  font-weight: bold;
  margin-right: 4px;
}

.config-value {
  color: #303133;
}

.config-value code {
  background: #f5f7fa;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
  font-size: 11px;
}

.next-run-normal {
  color: #409EFF;
}

.next-run-soon {
  color: #E6A23C;
  font-weight: bold;
}

.next-run-overdue {
  color: #F56C6C;
  font-weight: bold;
}

.next-run-disabled {
  color: #C0C4CC;
}

/* 主要布局样式 */
.main-content {
  display: flex;
  height: calc(100vh - 120px);
  gap: 15px;
}

/* 视图侧边栏样式 */
.view-sidebar {
  width: 250px;
  background: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  overflow: hidden;
  flex-shrink: 0;
}

.view-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
}

.view-title {
  font-weight: 600;
  color: #303133;
  font-size: 14px;
}

.view-list {
  max-height: calc(100vh - 200px);
  overflow-y: auto;
}

.view-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 15px;
  cursor: pointer;
  border-bottom: 1px solid #f0f0f0;
  transition: all 0.3s;
}

.view-item:hover {
  background: #f5f7fa;
}

.view-item.active {
  background: #ecf5ff;
  border-right: 3px solid #409eff;
}

.view-info {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.view-name {
  font-size: 14px;
  color: #303133;
  font-weight: 500;
}

.view-count {
  font-size: 12px;
  color: #909399;
}

.view-actions {
  display: flex;
  gap: 5px;
  opacity: 0;
  transition: opacity 0.3s;
}

.view-item:hover .view-actions {
  opacity: 1;
}

.view-actions .el-button {
  padding: 5px;
  min-height: auto;
}

/* 任务内容区域样式 */
.task-content {
  flex: 1;
  background: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.task-content .filter-container {
  padding: 15px;
  border-bottom: 1px solid #e4e7ed;
  background: #f5f7fa;
}

/* 表单提示样式 */
.form-tip {
  margin-top: 5px;
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .view-sidebar {
    width: 200px;
  }
}

@media (max-width: 768px) {
  .main-content {
    flex-direction: column;
    height: auto;
  }

  .view-sidebar {
    width: 100%;
    height: auto;
    max-height: 200px;
  }

  .view-list {
    max-height: 150px;
  }
}
</style>
