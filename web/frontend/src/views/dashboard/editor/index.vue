<template>
  <div class="dashboard-container">
    <div class="welcome-card">
      <h1>欢迎回来，{{ name }}！</h1>
      <p>当前角色：{{ roles.join(', ') }}</p>
      <p>今天是 {{ currentDate }}</p>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'DashboardEditor',
  components: { },
  data() {
    return {
      currentDate: new Date().toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        weekday: 'long'
      })
    }
  },
  computed: {
    ...mapGetters([
      'name',
      'roles'
    ])
  }
}
</script>

<style lang="scss" scoped>
.dashboard-container {
  padding: 32px;
  background-color: rgb(240, 242, 245);
  min-height: calc(100vh - 84px);
  display: flex;
  align-items: center;
  justify-content: center;

  .welcome-card {
    background: #fff;
    padding: 40px;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    text-align: center;
    max-width: 600px;
    width: 100%;

    h1 {
      color: #409EFF;
      margin-bottom: 20px;
      font-size: 28px;
    }

    p {
      color: #606266;
      font-size: 16px;
      margin: 10px 0;
    }
  }
}
</style>
