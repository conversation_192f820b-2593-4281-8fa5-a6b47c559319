# 投放自动化功能说明

## 📋 新增菜单结构

```
投放自动化 (el-icon-s-marketing)
├── 投放数据 (el-icon-data-line)
├── 小游戏数据 (el-icon-trophy)
└── 配置管理 (el-icon-setting)
```

## 🎯 功能特性

### 1. 投放数据页面 (`/advertising/campaign-data`)

**主要功能：**
- 📊 投放活动列表展示
- 🔍 多条件筛选（活动名称、平台、状态）
- 📱 支持多平台（Facebook、Google、TikTok、微信）
- 💰 预算和时间管理
- 📈 状态管理（进行中、已暂停、已完成）
- 📤 数据导出功能

**页面特点：**
- 响应式表格设计
- 实时状态更新
- 颜色标签区分平台和状态
- 分页显示

### 2. 小游戏数据页面 (`/advertising/game-data`)

**主要功能：**
- 📈 数据概览卡片（总游戏数、总播放次数、总收入、平均转化率）
- 🎮 游戏数据列表
- 📅 日期范围筛选
- 📊 转化率分析（高/中/低转化率颜色区分）
- 💵 收入统计
- 🔍 游戏详情查看

**页面特点：**
- 美观的统计卡片
- 转化率颜色编码
- 数据可视化友好
- 支持游戏分析功能

### 3. 配置管理页面 (`/advertising/config-management`)

**主要功能：**
- ⚙️ 筛选配置项管理
- 🏷️ 配置分类（平台配置、游戏配置、通知配置）
- ✏️ 在线编辑配置值
- 📝 配置描述管理
- 🔄 实时保存和取消
- ➕ 新增配置项

**页面特点：**
- 行内编辑功能
- 分类标签管理
- 表单验证
- 对话框新增/编辑

## 🗄️ 数据库设计建议

### 推荐方案：分离数据库

```sql
-- 创建投放数据库 advertising.db
CREATE DATABASE advertising;

-- 投放活动表
CREATE TABLE ad_campaigns (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    campaign_name TEXT NOT NULL,
    platform TEXT NOT NULL,  -- facebook, google, tiktok, wechat
    budget DECIMAL(10,2) NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    status TEXT DEFAULT 'active',  -- active, paused, completed
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 小游戏数据表
CREATE TABLE game_data (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    game_name TEXT NOT NULL,
    play_count INTEGER DEFAULT 0,
    conversion_rate DECIMAL(5,2) DEFAULT 0.00,
    revenue DECIMAL(10,2) DEFAULT 0.00,
    date DATE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 配置管理表
CREATE TABLE ad_configs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    config_key TEXT UNIQUE NOT NULL,
    config_value TEXT NOT NULL,
    description TEXT,
    category TEXT DEFAULT 'system',  -- platform, game, system, notification
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 🚀 后端API接口建议

### 投放数据相关接口
```
GET    /api/advertising/campaigns          # 获取投放活动列表
POST   /api/advertising/campaigns          # 创建投放活动
PUT    /api/advertising/campaigns/{id}     # 更新投放活动
DELETE /api/advertising/campaigns/{id}     # 删除投放活动
```

### 小游戏数据相关接口
```
GET    /api/advertising/games              # 获取游戏数据列表
GET    /api/advertising/games/stats        # 获取游戏统计数据
GET    /api/advertising/games/{id}/detail  # 获取游戏详情
POST   /api/advertising/games              # 添加游戏数据
```

### 配置管理相关接口
```
GET    /api/advertising/configs            # 获取配置列表
POST   /api/advertising/configs            # 创建配置项
PUT    /api/advertising/configs/{id}       # 更新配置项
DELETE /api/advertising/configs/{id}       # 删除配置项
```

## 📁 文件结构

```
src/
├── router/
│   └── modules/
│       └── advertising.js              # 投放自动化路由配置
└── views/
    └── advertising/                     # 投放自动化页面目录
        ├── campaign-data/
        │   └── index.vue               # 投放数据页面
        ├── game-data/
        │   └── index.vue               # 小游戏数据页面
        └── config-management/
            └── index.vue               # 配置管理页面
```

## 🎨 UI特色

- **现代化设计**：使用Element UI组件库
- **响应式布局**：适配不同屏幕尺寸
- **颜色编码**：不同状态和类型使用不同颜色
- **交互友好**：支持行内编辑、实时搜索等
- **数据可视化**：统计卡片、进度条等

## 🔧 技术栈

- **前端框架**：Vue.js 2.x
- **UI组件库**：Element UI
- **路由管理**：Vue Router
- **状态管理**：Vuex
- **HTTP客户端**：Axios

## 📝 使用说明

1. 启动前端服务后，在侧边栏可以看到"投放自动化"菜单
2. 点击展开可以看到三个子菜单
3. 每个页面都有完整的CRUD功能模拟
4. 所有数据目前都是模拟数据，等待后端API开发完成后替换

## 🚧 待开发功能

- [ ] 后端API接口开发
- [ ] 真实数据对接
- [ ] 数据图表可视化
- [ ] 导出功能实现
- [ ] 权限控制细化
- [ ] 实时数据更新
